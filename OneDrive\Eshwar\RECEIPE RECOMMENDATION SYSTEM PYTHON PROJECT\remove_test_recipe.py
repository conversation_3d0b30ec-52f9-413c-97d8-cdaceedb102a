#!/usr/bin/env python
"""
Remove test recipe "sss" safely
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'recipe_recommender.settings')
django.setup()

from recipes.models import Recipe

def remove_test_recipe():
    print("🧹 Removing test recipe...")
    print("=" * 30)
    
    # Remove test recipe "sss"
    test_recipes = Recipe.objects.filter(title='sss')
    if test_recipes.exists():
        for recipe in test_recipes:
            print(f"🗑️  Removing test recipe: {recipe.title}")
            recipe.delete()
        print("✅ Test recipe removed!")
    else:
        print("ℹ️  No test recipe found")
    
    # Show final count and list
    final_count = Recipe.objects.count()
    print(f"\n📊 Final recipe count: {final_count}")
    
    print(f"\n🍛 All recipes (South Indian focus):")
    for recipe in Recipe.objects.all().order_by('title'):
        dietary_badge = "🌱" if recipe.dietary_restrictions == 'vegan' else "🥛" if recipe.dietary_restrictions == 'vegetarian' else "🌾" if recipe.dietary_restrictions == 'gluten_free' else "🥥" if recipe.dietary_restrictions == 'dairy_free' else "🍽️"
        difficulty_badge = "🟢" if recipe.difficulty == 'easy' else "🟡" if recipe.difficulty == 'medium' else "🔴"
        print(f"  {dietary_badge} {difficulty_badge} {recipe.title} ({recipe.cooking_time} min)")

if __name__ == "__main__":
    remove_test_recipe()
