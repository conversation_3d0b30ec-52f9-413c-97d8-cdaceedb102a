#!/usr/bin/env python3
"""
Create admin user with proper permissions for analytics access.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'recipe_recommender.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
django.setup()

from django.contrib.auth.models import User


def create_admin_user():
    """Create or update admin user with proper permissions."""
    print("👤 Creating Admin User for Analytics Access")
    print("-" * 45)
    
    # Check if admin user exists
    try:
        admin_user = User.objects.get(username='admin')
        print(f"✅ Admin user already exists: {admin_user.username}")
        
        # Update permissions
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.is_active = True
        admin_user.save()
        print("✅ Updated admin permissions")
        
    except User.DoesNotExist:
        # Create new admin user
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print(f"✅ Created new admin user: {admin_user.username}")
    
    # Check testchef user and update permissions
    try:
        testchef_user = User.objects.get(username='testchef')
        print(f"👨‍🍳 Found testchef user: {testchef_user.username}")
        
        # Give testchef staff permissions too
        testchef_user.is_staff = True
        testchef_user.save()
        print("✅ Updated testchef with staff permissions")
        
    except User.DoesNotExist:
        print("ℹ️  testchef user not found")
    
    return admin_user


def display_user_info():
    """Display information about available users."""
    print("\n👥 Available Users")
    print("-" * 20)
    
    users = User.objects.all()
    for user in users:
        status = []
        if user.is_superuser:
            status.append("Superuser")
        if user.is_staff:
            status.append("Staff")
        if user.is_active:
            status.append("Active")
        
        status_str = ", ".join(status) if status else "Regular User"
        print(f"   👤 {user.username} ({user.email}) - {status_str}")


def main():
    """Create admin user and display login information."""
    print("🔐 Admin User Setup for Recipe Recommender Analytics")
    print("=" * 55)
    print()
    
    # Create admin user
    admin_user = create_admin_user()
    
    # Display user information
    display_user_info()
    
    print("\n🔑 Login Credentials")
    print("-" * 20)
    print("Admin User:")
    print("   Username: admin")
    print("   Password: admin123")
    print()
    print("TestChef User (now with staff access):")
    print("   Username: testchef")
    print("   Password: [your existing password]")
    
    print("\n🔗 Access Instructions")
    print("-" * 20)
    print("1. Go to: http://127.0.0.1:8001/admin/")
    print("2. Login with admin credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("3. Navigate to Analytics section")
    print("4. Test the category and difficulty filters!")
    
    print("\n📊 Available Analytics Sections")
    print("-" * 30)
    print("   • User Activities - with category/difficulty filters")
    print("   • Recipe Analytics - with performance metrics")
    print("   • Search Analytics - with query categorization")
    print("   • Daily Statistics - with trend analysis")
    
    print("\n🎯 Filter Options Available")
    print("-" * 25)
    print("   🗂️  Recipe Category:")
    print("      - Breakfast Items")
    print("      - Main Course")
    print("      - Snacks & Appetizers")
    print("      - Sweets & Desserts")
    print("      - Rice Dishes")
    print("      - Curry & Gravy")
    print("      - Traditional Beverages")
    print("      - Festival Specials")
    print()
    print("   ⚡ Recipe Difficulty:")
    print("      - Easy (15-30 min)")
    print("      - Medium (30-60 min)")
    print("      - Hard (60+ min)")
    print("      - Quick Recipes (≤20 min)")
    print("      - Complex Recipes (≥45 min)")
    print()
    print("   🥗 Dietary Restrictions:")
    print("      - No Restrictions")
    print("      - Vegetarian")
    print("      - Vegan")
    print("      - Gluten Free")
    print("      - Dairy Free")
    
    print("\n✅ Setup completed! You can now access the analytics admin interface.")


if __name__ == "__main__":
    main()
