# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-19 20:23+0200\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: conf/global_settings.py:52
msgid "Afrikaans"
msgstr ""

#: conf/global_settings.py:53
msgid "Arabic"
msgstr ""

#: conf/global_settings.py:54
msgid "Algerian Arabic"
msgstr ""

#: conf/global_settings.py:55
msgid "Asturian"
msgstr ""

#: conf/global_settings.py:56
msgid "Azerbaijani"
msgstr ""

#: conf/global_settings.py:57
msgid "Bulgarian"
msgstr ""

#: conf/global_settings.py:58
msgid "Belarusian"
msgstr ""

#: conf/global_settings.py:59
msgid "Bengali"
msgstr ""

#: conf/global_settings.py:60
msgid "Breton"
msgstr ""

#: conf/global_settings.py:61
msgid "Bosnian"
msgstr ""

#: conf/global_settings.py:62
msgid "Catalan"
msgstr ""

#: conf/global_settings.py:63
msgid "Czech"
msgstr ""

#: conf/global_settings.py:64
msgid "Welsh"
msgstr ""

#: conf/global_settings.py:65
msgid "Danish"
msgstr ""

#: conf/global_settings.py:66
msgid "German"
msgstr ""

#: conf/global_settings.py:67
msgid "Lower Sorbian"
msgstr ""

#: conf/global_settings.py:68
msgid "Greek"
msgstr ""

#: conf/global_settings.py:69
msgid "English"
msgstr ""

#: conf/global_settings.py:70
msgid "Australian English"
msgstr ""

#: conf/global_settings.py:71
msgid "British English"
msgstr ""

#: conf/global_settings.py:72
msgid "Esperanto"
msgstr ""

#: conf/global_settings.py:73
msgid "Spanish"
msgstr ""

#: conf/global_settings.py:74
msgid "Argentinian Spanish"
msgstr ""

#: conf/global_settings.py:75
msgid "Colombian Spanish"
msgstr ""

#: conf/global_settings.py:76
msgid "Mexican Spanish"
msgstr ""

#: conf/global_settings.py:77
msgid "Nicaraguan Spanish"
msgstr ""

#: conf/global_settings.py:78
msgid "Venezuelan Spanish"
msgstr ""

#: conf/global_settings.py:79
msgid "Estonian"
msgstr ""

#: conf/global_settings.py:80
msgid "Basque"
msgstr ""

#: conf/global_settings.py:81
msgid "Persian"
msgstr ""

#: conf/global_settings.py:82
msgid "Finnish"
msgstr ""

#: conf/global_settings.py:83
msgid "French"
msgstr ""

#: conf/global_settings.py:84
msgid "Frisian"
msgstr ""

#: conf/global_settings.py:85
msgid "Irish"
msgstr ""

#: conf/global_settings.py:86
msgid "Scottish Gaelic"
msgstr ""

#: conf/global_settings.py:87
msgid "Galician"
msgstr ""

#: conf/global_settings.py:88
msgid "Hebrew"
msgstr ""

#: conf/global_settings.py:89
msgid "Hindi"
msgstr ""

#: conf/global_settings.py:90
msgid "Croatian"
msgstr ""

#: conf/global_settings.py:91
msgid "Upper Sorbian"
msgstr ""

#: conf/global_settings.py:92
msgid "Hungarian"
msgstr ""

#: conf/global_settings.py:93
msgid "Armenian"
msgstr ""

#: conf/global_settings.py:94
msgid "Interlingua"
msgstr ""

#: conf/global_settings.py:95
msgid "Indonesian"
msgstr ""

#: conf/global_settings.py:96
msgid "Igbo"
msgstr ""

#: conf/global_settings.py:96
msgid "Ido"
msgstr ""

#: conf/global_settings.py:97
msgid "Icelandic"
msgstr ""

#: conf/global_settings.py:98
msgid "Italian"
msgstr ""

#: conf/global_settings.py:99
msgid "Japanese"
msgstr ""

#: conf/global_settings.py:100
msgid "Georgian"
msgstr ""

#: conf/global_settings.py:101
msgid "Kabyle"
msgstr ""

#: conf/global_settings.py:102
msgid "Kazakh"
msgstr ""

#: conf/global_settings.py:103
msgid "Khmer"
msgstr ""

#: conf/global_settings.py:104
msgid "Kannada"
msgstr ""

#: conf/global_settings.py:105
msgid "Korean"
msgstr ""

#: conf/global_settings.py:106
msgid "Kyrgyz"
msgstr ""

#: conf/global_settings.py:106
msgid "Luxembourgish"
msgstr ""

#: conf/global_settings.py:107
msgid "Lithuanian"
msgstr ""

#: conf/global_settings.py:108
msgid "Latvian"
msgstr ""

#: conf/global_settings.py:109
msgid "Macedonian"
msgstr ""

#: conf/global_settings.py:110
msgid "Malayalam"
msgstr ""

#: conf/global_settings.py:111
msgid "Mongolian"
msgstr ""

#: conf/global_settings.py:112
msgid "Marathi"
msgstr ""

#: conf/global_settings.py:113
msgid "Burmese"
msgstr ""

#: conf/global_settings.py:114
msgid "Norwegian Bokmål"
msgstr ""

#: conf/global_settings.py:115
msgid "Nepali"
msgstr ""

#: conf/global_settings.py:116
msgid "Dutch"
msgstr ""

#: conf/global_settings.py:117
msgid "Norwegian Nynorsk"
msgstr ""

#: conf/global_settings.py:118
msgid "Ossetic"
msgstr ""

#: conf/global_settings.py:119
msgid "Punjabi"
msgstr ""

#: conf/global_settings.py:120
msgid "Polish"
msgstr ""

#: conf/global_settings.py:121
msgid "Portuguese"
msgstr ""

#: conf/global_settings.py:122
msgid "Brazilian Portuguese"
msgstr ""

#: conf/global_settings.py:123
msgid "Romanian"
msgstr ""

#: conf/global_settings.py:124
msgid "Russian"
msgstr ""

#: conf/global_settings.py:125
msgid "Slovak"
msgstr ""

#: conf/global_settings.py:126
msgid "Slovenian"
msgstr ""

#: conf/global_settings.py:127
msgid "Albanian"
msgstr ""

#: conf/global_settings.py:128
msgid "Serbian"
msgstr ""

#: conf/global_settings.py:129
msgid "Serbian Latin"
msgstr ""

#: conf/global_settings.py:130
msgid "Swedish"
msgstr ""

#: conf/global_settings.py:131
msgid "Swahili"
msgstr ""

#: conf/global_settings.py:132
msgid "Tamil"
msgstr ""

#: conf/global_settings.py:133
msgid "Telugu"
msgstr ""

#: conf/global_settings.py:133
msgid "Tajik"
msgstr ""

#: conf/global_settings.py:134
msgid "Thai"
msgstr ""

#: conf/global_settings.py:135
msgid "Turkmen"
msgstr ""

#: conf/global_settings.py:135
msgid "Turkish"
msgstr ""

#: conf/global_settings.py:136
msgid "Tatar"
msgstr ""

#: conf/global_settings.py:137
msgid "Udmurt"
msgstr ""

#: conf/global_settings.py:138
msgid "Ukrainian"
msgstr ""

#: conf/global_settings.py:139
msgid "Urdu"
msgstr ""

#: conf/global_settings.py:140
msgid "Uzbek"
msgstr ""

#: conf/global_settings.py:141
msgid "Vietnamese"
msgstr ""

#: conf/global_settings.py:142
msgid "Simplified Chinese"
msgstr ""

#: conf/global_settings.py:143
msgid "Traditional Chinese"
msgstr ""

#: contrib/messages/apps.py:7
msgid "Messages"
msgstr ""

#: contrib/sitemaps/apps.py:7
msgid "Site Maps"
msgstr ""

#: contrib/staticfiles/apps.py:9
msgid "Static Files"
msgstr ""

#: contrib/syndication/apps.py:7
msgid "Syndication"
msgstr ""

#: core/paginator.py:48
msgid "That page number is not an integer"
msgstr ""

#: core/paginator.py:50
msgid "That page number is less than 1"
msgstr ""

#: core/paginator.py:55
msgid "That page contains no results"
msgstr ""

#: core/validators.py:20
msgid "Enter a valid value."
msgstr ""

#: core/validators.py:91 forms/fields.py:671
msgid "Enter a valid URL."
msgstr ""

#: core/validators.py:145
msgid "Enter a valid integer."
msgstr ""

#: core/validators.py:156
msgid "Enter a valid email address."
msgstr ""

#. Translators: "letters" means latin letters: a-z and A-Z.
#: core/validators.py:230
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""

#: core/validators.py:237
msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""

#: core/validators.py:246 core/validators.py:266
msgid "Enter a valid IPv4 address."
msgstr ""

#: core/validators.py:251 core/validators.py:267
msgid "Enter a valid IPv6 address."
msgstr ""

#: core/validators.py:261 core/validators.py:265
msgid "Enter a valid IPv4 or IPv6 address."
msgstr ""

#: core/validators.py:295
msgid "Enter only digits separated by commas."
msgstr ""

#: core/validators.py:301
#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""

#: core/validators.py:334
#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr ""

#: core/validators.py:343
#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr ""

#: core/validators.py:353
#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: core/validators.py:368
#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
msgstr[1] ""

#: core/validators.py:387 forms/fields.py:292 forms/fields.py:327
msgid "Enter a number."
msgstr ""

#: core/validators.py:389
#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""
msgstr[1] ""

#: core/validators.py:394
#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""
msgstr[1] ""

#: core/validators.py:399
#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""
msgstr[1] ""

#: core/validators.py:461
#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""

#: core/validators.py:513
msgid "Null characters are not allowed."
msgstr ""

#: db/models/base.py:1187 forms/models.py:760
msgid "and"
msgstr ""

#: db/models/base.py:1189
#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr ""

#: db/models/fields/__init__.py:100
#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr ""

#: db/models/fields/__init__.py:101
msgid "This field cannot be null."
msgstr ""

#: db/models/fields/__init__.py:102
msgid "This field cannot be blank."
msgstr ""

#: db/models/fields/__init__.py:103
#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr ""

#. Translators: The 'lookup_type' is one of 'date', 'year' or 'month'.
#. Eg: "Title must be unique for pub_date year"
#: db/models/fields/__init__.py:107
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""

#: db/models/fields/__init__.py:126
#, python-format
msgid "Field of type: %(field_type)s"
msgstr ""

#: db/models/fields/__init__.py:939
#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr ""

#: db/models/fields/__init__.py:940
#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr ""

#: db/models/fields/__init__.py:942
msgid "Boolean (Either True or False)"
msgstr ""

#: db/models/fields/__init__.py:983
#, python-format
msgid "String (up to %(max_length)s)"
msgstr ""

#: db/models/fields/__init__.py:1047
msgid "Comma-separated integers"
msgstr ""

#: db/models/fields/__init__.py:1096
#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""

#: db/models/fields/__init__.py:1098 db/models/fields/__init__.py:1241
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""

#: db/models/fields/__init__.py:1101
msgid "Date (without time)"
msgstr ""

#: db/models/fields/__init__.py:1239
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD HH:MM[:ss[."
"uuuuuu]][TZ] format."
msgstr ""

#: db/models/fields/__init__.py:1243
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""

#: db/models/fields/__init__.py:1247
msgid "Date (with time)"
msgstr ""

#: db/models/fields/__init__.py:1395
#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr ""

#: db/models/fields/__init__.py:1397
msgid "Decimal number"
msgstr ""

#: db/models/fields/__init__.py:1536
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] [[HH:]MM:]ss[."
"uuuuuu] format."
msgstr ""

#: db/models/fields/__init__.py:1539
msgid "Duration"
msgstr ""

#: db/models/fields/__init__.py:1589
msgid "Email address"
msgstr ""

#: db/models/fields/__init__.py:1612
msgid "File path"
msgstr ""

#: db/models/fields/__init__.py:1678
#, python-format
msgid "“%(value)s” value must be a float."
msgstr ""

#: db/models/fields/__init__.py:1680
msgid "Floating point number"
msgstr ""

#: db/models/fields/__init__.py:1718
#, python-format
msgid "“%(value)s” value must be an integer."
msgstr ""

#: db/models/fields/__init__.py:1720
msgid "Integer"
msgstr ""

#: db/models/fields/__init__.py:1803
msgid "Big (8 byte) integer"
msgstr ""

#: db/models/fields/__init__.py:1819
msgid "IPv4 address"
msgstr ""

#: db/models/fields/__init__.py:1850
msgid "IP address"
msgstr ""

#: db/models/fields/__init__.py:1930 db/models/fields/__init__.py:1931
#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr ""

#: db/models/fields/__init__.py:1933
msgid "Boolean (Either True, False or None)"
msgstr ""

#: db/models/fields/__init__.py:1976
msgid "Positive big integer"
msgstr ""

#: db/models/fields/__init__.py:1989
msgid "Positive integer"
msgstr ""

#: db/models/fields/__init__.py:2002
msgid "Positive small integer"
msgstr ""

#: db/models/fields/__init__.py:2016
#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr ""

#: db/models/fields/__init__.py:2048
msgid "Small integer"
msgstr ""

#: db/models/fields/__init__.py:2055
msgid "Text"
msgstr ""

#: db/models/fields/__init__.py:2083
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""

#: db/models/fields/__init__.py:2085
#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""

#: db/models/fields/__init__.py:2088
msgid "Time"
msgstr ""

#: db/models/fields/__init__.py:2214
msgid "URL"
msgstr ""

#: db/models/fields/__init__.py:2236
msgid "Raw binary data"
msgstr ""

#: db/models/fields/__init__.py:2301
#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr ""

#: db/models/fields/__init__.py:2303
msgid "Universally unique identifier"
msgstr ""

#: db/models/fields/files.py:231
msgid "File"
msgstr ""

#: db/models/fields/files.py:377
msgid "Image"
msgstr ""

#: db/models/fields/json.py:18
msgid "A JSON object"
msgstr ""

#: db/models/fields/json.py:20
msgid "Value must be valid JSON."
msgstr ""

#: db/models/fields/related.py:786
#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr ""

#: db/models/fields/related.py:788
msgid "Foreign Key (type determined by related field)"
msgstr ""

#: db/models/fields/related.py:1041
msgid "One-to-one relationship"
msgstr ""

#: db/models/fields/related.py:1095
#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr ""

#: db/models/fields/related.py:1096
#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr ""

#: db/models/fields/related.py:1138
msgid "Many-to-many relationship"
msgstr ""

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the label
#: forms/boundfield.py:150
msgid ":?.!"
msgstr ""

#: forms/fields.py:54
msgid "This field is required."
msgstr ""

#: forms/fields.py:247
msgid "Enter a whole number."
msgstr ""

#: forms/fields.py:398 forms/fields.py:1139
msgid "Enter a valid date."
msgstr ""

#: forms/fields.py:422 forms/fields.py:1140
msgid "Enter a valid time."
msgstr ""

#: forms/fields.py:450
msgid "Enter a valid date/time."
msgstr ""

#: forms/fields.py:484
msgid "Enter a valid duration."
msgstr ""

#: forms/fields.py:485
#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr ""

#: forms/fields.py:545
msgid "No file was submitted. Check the encoding type on the form."
msgstr ""

#: forms/fields.py:546
msgid "No file was submitted."
msgstr ""

#: forms/fields.py:547
msgid "The submitted file is empty."
msgstr ""

#: forms/fields.py:549
#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""
msgstr[1] ""

#: forms/fields.py:552
msgid "Please either submit a file or check the clear checkbox, not both."
msgstr ""

#: forms/fields.py:613
msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""

#: forms/fields.py:775 forms/fields.py:865 forms/models.py:1296
#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr ""

#: forms/fields.py:866 forms/fields.py:981 forms/models.py:1295
msgid "Enter a list of values."
msgstr ""

#: forms/fields.py:982
msgid "Enter a complete value."
msgstr ""

#: forms/fields.py:1198
msgid "Enter a valid UUID."
msgstr ""

#: forms/fields.py:1228
msgid "Enter a valid JSON."
msgstr ""

#. Translators: This is the default suffix added to form field labels
#: forms/forms.py:78
msgid ":"
msgstr ""

#: forms/forms.py:205
#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr ""

#: forms/formsets.py:93
msgid "ManagementForm data is missing or has been tampered with"
msgstr ""

#: forms/formsets.py:345
#, python-format
msgid "Please submit %d or fewer forms."
msgid_plural "Please submit %d or fewer forms."
msgstr[0] ""
msgstr[1] ""

#: forms/formsets.py:352
#, python-format
msgid "Please submit %d or more forms."
msgid_plural "Please submit %d or more forms."
msgstr[0] ""
msgstr[1] ""

#: forms/formsets.py:379 forms/formsets.py:386
msgid "Order"
msgstr ""

#: forms/formsets.py:391
msgid "Delete"
msgstr ""

#: forms/models.py:755
#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr ""

#: forms/models.py:759
#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""

#: forms/models.py:765
#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""

#: forms/models.py:774
msgid "Please correct the duplicate values below."
msgstr ""

#: forms/models.py:1096
msgid "The inline value did not match the parent instance."
msgstr ""

#: forms/models.py:1180
msgid "Select a valid choice. That choice is not one of the available choices."
msgstr ""

#: forms/models.py:1298
#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr ""

#: forms/utils.py:167
#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""

#: forms/widgets.py:398
msgid "Clear"
msgstr ""

#: forms/widgets.py:399
msgid "Currently"
msgstr ""

#: forms/widgets.py:400
msgid "Change"
msgstr ""

#: forms/widgets.py:709
msgid "Unknown"
msgstr ""

#: forms/widgets.py:710
msgid "Yes"
msgstr ""

#: forms/widgets.py:711
msgid "No"
msgstr ""

#. Translators: Please do not add spaces around commas.
#: template/defaultfilters.py:790
msgid "yes,no,maybe"
msgstr ""

#: template/defaultfilters.py:819 template/defaultfilters.py:836
#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] ""
msgstr[1] ""

#: template/defaultfilters.py:838
#, python-format
msgid "%s KB"
msgstr ""

#: template/defaultfilters.py:840
#, python-format
msgid "%s MB"
msgstr ""

#: template/defaultfilters.py:842
#, python-format
msgid "%s GB"
msgstr ""

#: template/defaultfilters.py:844
#, python-format
msgid "%s TB"
msgstr ""

#: template/defaultfilters.py:846
#, python-format
msgid "%s PB"
msgstr ""

#: utils/dateformat.py:65
msgid "p.m."
msgstr ""

#: utils/dateformat.py:66
msgid "a.m."
msgstr ""

#: utils/dateformat.py:71
msgid "PM"
msgstr ""

#: utils/dateformat.py:72
msgid "AM"
msgstr ""

#: utils/dateformat.py:149
msgid "midnight"
msgstr ""

#: utils/dateformat.py:151
msgid "noon"
msgstr ""

#: utils/dates.py:6
msgid "Monday"
msgstr ""

#: utils/dates.py:6
msgid "Tuesday"
msgstr ""

#: utils/dates.py:6
msgid "Wednesday"
msgstr ""

#: utils/dates.py:6
msgid "Thursday"
msgstr ""

#: utils/dates.py:6
msgid "Friday"
msgstr ""

#: utils/dates.py:7
msgid "Saturday"
msgstr ""

#: utils/dates.py:7
msgid "Sunday"
msgstr ""

#: utils/dates.py:10
msgid "Mon"
msgstr ""

#: utils/dates.py:10
msgid "Tue"
msgstr ""

#: utils/dates.py:10
msgid "Wed"
msgstr ""

#: utils/dates.py:10
msgid "Thu"
msgstr ""

#: utils/dates.py:10
msgid "Fri"
msgstr ""

#: utils/dates.py:11
msgid "Sat"
msgstr ""

#: utils/dates.py:11
msgid "Sun"
msgstr ""

#: utils/dates.py:14
msgid "January"
msgstr ""

#: utils/dates.py:14
msgid "February"
msgstr ""

#: utils/dates.py:14
msgid "March"
msgstr ""

#: utils/dates.py:14
msgid "April"
msgstr ""

#: utils/dates.py:14
msgid "May"
msgstr ""

#: utils/dates.py:14
msgid "June"
msgstr ""

#: utils/dates.py:15
msgid "July"
msgstr ""

#: utils/dates.py:15
msgid "August"
msgstr ""

#: utils/dates.py:15
msgid "September"
msgstr ""

#: utils/dates.py:15
msgid "October"
msgstr ""

#: utils/dates.py:15
msgid "November"
msgstr ""

#: utils/dates.py:16
msgid "December"
msgstr ""

#: utils/dates.py:19
msgid "jan"
msgstr ""

#: utils/dates.py:19
msgid "feb"
msgstr ""

#: utils/dates.py:19
msgid "mar"
msgstr ""

#: utils/dates.py:19
msgid "apr"
msgstr ""

#: utils/dates.py:19
msgid "may"
msgstr ""

#: utils/dates.py:19
msgid "jun"
msgstr ""

#: utils/dates.py:20
msgid "jul"
msgstr ""

#: utils/dates.py:20
msgid "aug"
msgstr ""

#: utils/dates.py:20
msgid "sep"
msgstr ""

#: utils/dates.py:20
msgid "oct"
msgstr ""

#: utils/dates.py:20
msgid "nov"
msgstr ""

#: utils/dates.py:20
msgid "dec"
msgstr ""

#: utils/dates.py:23
msgctxt "abbrev. month"
msgid "Jan."
msgstr ""

#: utils/dates.py:24
msgctxt "abbrev. month"
msgid "Feb."
msgstr ""

#: utils/dates.py:25
msgctxt "abbrev. month"
msgid "March"
msgstr ""

#: utils/dates.py:26
msgctxt "abbrev. month"
msgid "April"
msgstr ""

#: utils/dates.py:27
msgctxt "abbrev. month"
msgid "May"
msgstr ""

#: utils/dates.py:28
msgctxt "abbrev. month"
msgid "June"
msgstr ""

#: utils/dates.py:29
msgctxt "abbrev. month"
msgid "July"
msgstr ""

#: utils/dates.py:30
msgctxt "abbrev. month"
msgid "Aug."
msgstr ""

#: utils/dates.py:31
msgctxt "abbrev. month"
msgid "Sept."
msgstr ""

#: utils/dates.py:32
msgctxt "abbrev. month"
msgid "Oct."
msgstr ""

#: utils/dates.py:33
msgctxt "abbrev. month"
msgid "Nov."
msgstr ""

#: utils/dates.py:34
msgctxt "abbrev. month"
msgid "Dec."
msgstr ""

#: utils/dates.py:37
msgctxt "alt. month"
msgid "January"
msgstr ""

#: utils/dates.py:38
msgctxt "alt. month"
msgid "February"
msgstr ""

#: utils/dates.py:39
msgctxt "alt. month"
msgid "March"
msgstr ""

#: utils/dates.py:40
msgctxt "alt. month"
msgid "April"
msgstr ""

#: utils/dates.py:41
msgctxt "alt. month"
msgid "May"
msgstr ""

#: utils/dates.py:42
msgctxt "alt. month"
msgid "June"
msgstr ""

#: utils/dates.py:43
msgctxt "alt. month"
msgid "July"
msgstr ""

#: utils/dates.py:44
msgctxt "alt. month"
msgid "August"
msgstr ""

#: utils/dates.py:45
msgctxt "alt. month"
msgid "September"
msgstr ""

#: utils/dates.py:46
msgctxt "alt. month"
msgid "October"
msgstr ""

#: utils/dates.py:47
msgctxt "alt. month"
msgid "November"
msgstr ""

#: utils/dates.py:48
msgctxt "alt. month"
msgid "December"
msgstr ""

#: utils/ipv6.py:8
msgid "This is not a valid IPv6 address."
msgstr ""

#: utils/text.py:70
#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr ""

#: utils/text.py:236
msgid "or"
msgstr ""

#. Translators: This string is used as a separator between list elements
#: utils/text.py:255 utils/timesince.py:83
msgid ", "
msgstr ""

#: utils/timesince.py:9
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] ""
msgstr[1] ""

#: utils/timesince.py:10
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] ""
msgstr[1] ""

#: utils/timesince.py:11
#, python-format
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] ""
msgstr[1] ""

#: utils/timesince.py:12
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""
msgstr[1] ""

#: utils/timesince.py:13
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#: utils/timesince.py:14
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

#: views/csrf.py:110
msgid "Forbidden"
msgstr ""

#: views/csrf.py:111
msgid "CSRF verification failed. Request aborted."
msgstr ""

#: views/csrf.py:115
msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your Web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""

#: views/csrf.py:120
msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""

#: views/csrf.py:124
msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a rel=\"noreferrer"
"\" …> for links to third-party sites."
msgstr ""

#: views/csrf.py:132
msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""

#: views/csrf.py:137
msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""

#: views/csrf.py:142
msgid "More information is available with DEBUG=True."
msgstr ""

#: views/generic/dates.py:41
msgid "No year specified"
msgstr ""

#: views/generic/dates.py:61 views/generic/dates.py:111
#: views/generic/dates.py:208
msgid "Date out of range"
msgstr ""

#: views/generic/dates.py:90
msgid "No month specified"
msgstr ""

#: views/generic/dates.py:142
msgid "No day specified"
msgstr ""

#: views/generic/dates.py:188
msgid "No week specified"
msgstr ""

#: views/generic/dates.py:338 views/generic/dates.py:367
#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr ""

#: views/generic/dates.py:589
#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because %(class_name)s."
"allow_future is False."
msgstr ""

#: views/generic/dates.py:623
#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr ""

#: views/generic/detail.py:54
#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr ""

#: views/generic/list.py:67
msgid "Page is not “last”, nor can it be converted to an int."
msgstr ""

#: views/generic/list.py:72
#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr ""

#: views/generic/list.py:154
#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr ""

#: views/static.py:40
msgid "Directory indexes are not allowed here."
msgstr ""

#: views/static.py:42
#, python-format
msgid "“%(path)s” does not exist"
msgstr ""

#: views/static.py:80
#, python-format
msgid "Index of %(directory)s"
msgstr ""

#: views/templates/default_urlconf.html:7
msgid "Django: the Web framework for perfectionists with deadlines."
msgstr ""

#: views/templates/default_urlconf.html:346
#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""

#: views/templates/default_urlconf.html:368
msgid "The install worked successfully! Congratulations!"
msgstr ""

#: views/templates/default_urlconf.html:369
#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" rel=\"noopener"
"\">DEBUG=True</a> is in your settings file and you have not configured any "
"URLs."
msgstr ""

#: views/templates/default_urlconf.html:384
msgid "Django Documentation"
msgstr ""

#: views/templates/default_urlconf.html:385
msgid "Topics, references, &amp; how-to’s"
msgstr ""

#: views/templates/default_urlconf.html:396
msgid "Tutorial: A Polling App"
msgstr ""

#: views/templates/default_urlconf.html:397
msgid "Get started with Django"
msgstr ""

#: views/templates/default_urlconf.html:408
msgid "Django Community"
msgstr ""

#: views/templates/default_urlconf.html:409
msgid "Connect, get help, or contribute"
msgstr ""
