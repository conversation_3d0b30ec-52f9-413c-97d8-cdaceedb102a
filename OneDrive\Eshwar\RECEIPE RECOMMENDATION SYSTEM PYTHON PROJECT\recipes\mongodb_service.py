"""
MongoDB Service for Recipe Recommender
Handles MongoDB operations alongside Django ORM
"""
import pymongo
from pymongo import MongoClient
from django.conf import settings
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class MongoDBService:
    """Service class to handle MongoDB operations"""
    
    def __init__(self):
        self.client = None
        self.db = None
        self.connect()
    
    def connect(self):
        """Connect to MongoDB"""
        try:
            mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})
            host = mongodb_settings.get('host', 'mongodb://localhost:27017/')
            db_name = mongodb_settings.get('db_name', 'recipe_recommender_db')
            
            self.client = MongoClient(host)
            self.db = self.client[db_name]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"✅ Connected to MongoDB: {db_name}")
            
        except Exception as e:
            logger.error(f"❌ MongoDB connection failed: {e}")
            self.client = None
            self.db = None
    
    def is_connected(self):
        """Check if MongoDB is connected"""
        return self.client is not None and self.db is not None
    
    def sync_recipe_to_mongodb(self, recipe):
        """Sync a Django Recipe object to MongoDB"""
        if not self.is_connected():
            logger.warning("MongoDB not connected, skipping sync")
            return False
        
        try:
            recipe_data = {
                'django_id': recipe.id,
                'title': recipe.title,
                'description': recipe.description,
                'ingredients': recipe.ingredients.split('\n') if recipe.ingredients else [],
                'instructions': recipe.instructions.split('\n') if recipe.instructions else [],
                'cooking_time': recipe.cooking_time,
                'dietary_restrictions': recipe.dietary_restrictions,
                'difficulty': recipe.difficulty,
                'servings': recipe.servings,
                'author_username': recipe.author.username if recipe.author else None,
                'created_at': recipe.created_at,
                'view_count': recipe.view_count,
                'cooking_tips': recipe.cooking_tips,
                'nutritional_info': recipe.nutritional_info,
                'prep_time': recipe.prep_time,
                'total_time': recipe.total_time,
                'rating_average': float(recipe.rating_average),
                'rating_count': recipe.rating_count,
                'synced_at': datetime.now(),
                'tags': self._extract_tags(recipe),
                'category': self._determine_category(recipe),
            }
            
            # Upsert recipe (update if exists, insert if not)
            result = self.db.recipes.update_one(
                {'django_id': recipe.id},
                {'$set': recipe_data},
                upsert=True
            )
            
            logger.info(f"✅ Synced recipe '{recipe.title}' to MongoDB")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to sync recipe to MongoDB: {e}")
            return False
    
    def sync_all_recipes_to_mongodb(self):
        """Sync all Django recipes to MongoDB"""
        if not self.is_connected():
            logger.warning("MongoDB not connected, skipping sync")
            return False
        
        try:
            from .models import Recipe
            recipes = Recipe.objects.all()
            
            synced_count = 0
            for recipe in recipes:
                if self.sync_recipe_to_mongodb(recipe):
                    synced_count += 1
            
            logger.info(f"✅ Synced {synced_count}/{len(recipes)} recipes to MongoDB")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to sync all recipes: {e}")
            return False
    
    def search_recipes_in_mongodb(self, query, filters=None):
        """Search recipes in MongoDB with advanced filtering"""
        if not self.is_connected():
            return []
        
        try:
            # Build search criteria
            search_criteria = {}
            
            if query:
                search_criteria['$or'] = [
                    {'title': {'$regex': query, '$options': 'i'}},
                    {'description': {'$regex': query, '$options': 'i'}},
                    {'ingredients': {'$regex': query, '$options': 'i'}},
                    {'tags': {'$regex': query, '$options': 'i'}},
                ]
            
            if filters:
                if filters.get('dietary_restrictions'):
                    search_criteria['dietary_restrictions'] = filters['dietary_restrictions']
                
                if filters.get('difficulty'):
                    search_criteria['difficulty'] = filters['difficulty']
                
                if filters.get('max_cooking_time'):
                    search_criteria['cooking_time'] = {'$lte': int(filters['max_cooking_time'])}
                
                if filters.get('category'):
                    search_criteria['category'] = filters['category']
            
            # Execute search
            results = list(self.db.recipes.find(search_criteria).sort('view_count', -1))
            
            logger.info(f"🔍 MongoDB search returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"❌ MongoDB search failed: {e}")
            return []
    
    def get_popular_recipes(self, limit=10):
        """Get popular recipes from MongoDB"""
        if not self.is_connected():
            return []
        
        try:
            results = list(
                self.db.recipes.find()
                .sort('view_count', -1)
                .limit(limit)
            )
            return results
        except Exception as e:
            logger.error(f"❌ Failed to get popular recipes: {e}")
            return []
    
    def get_recipe_analytics(self):
        """Get recipe analytics from MongoDB"""
        if not self.is_connected():
            return {}
        
        try:
            pipeline = [
                {
                    '$group': {
                        '_id': None,
                        'total_recipes': {'$sum': 1},
                        'total_views': {'$sum': '$view_count'},
                        'avg_rating': {'$avg': '$rating_average'},
                        'avg_cooking_time': {'$avg': '$cooking_time'},
                    }
                },
                {
                    '$project': {
                        '_id': 0,
                        'total_recipes': 1,
                        'total_views': 1,
                        'avg_rating': {'$round': ['$avg_rating', 2]},
                        'avg_cooking_time': {'$round': ['$avg_cooking_time', 0]},
                    }
                }
            ]
            
            result = list(self.db.recipes.aggregate(pipeline))
            return result[0] if result else {}
            
        except Exception as e:
            logger.error(f"❌ Failed to get analytics: {e}")
            return {}
    
    def _extract_tags(self, recipe):
        """Extract tags from recipe for better searchability"""
        tags = []
        title_lower = recipe.title.lower()
        
        # Add cuisine type tags
        if any(word in title_lower for word in ['idli', 'dosa', 'vada', 'sambar', 'rasam']):
            tags.append('south_indian')
        
        # Add meal type tags
        if any(word in title_lower for word in ['breakfast', 'idli', 'dosa', 'upma']):
            tags.append('breakfast')
        elif any(word in title_lower for word in ['rice', 'sambar', 'curry']):
            tags.append('main_course')
        elif any(word in title_lower for word in ['snack', 'bonda', 'bajji']):
            tags.append('snack')
        
        # Add dietary tags
        if recipe.dietary_restrictions != 'none':
            tags.append(recipe.dietary_restrictions)
        
        # Add difficulty tags
        tags.append(f"{recipe.difficulty}_recipe")
        
        return tags
    
    def _determine_category(self, recipe):
        """Determine recipe category"""
        title_lower = recipe.title.lower()
        
        if any(word in title_lower for word in ['idli', 'dosa', 'upma', 'poha']):
            return 'breakfast'
        elif any(word in title_lower for word in ['rice', 'biryani', 'pulao']):
            return 'rice_dishes'
        elif any(word in title_lower for word in ['sambar', 'rasam', 'curry']):
            return 'curries'
        elif any(word in title_lower for word in ['chutney', 'pickle']):
            return 'accompaniments'
        elif any(word in title_lower for word in ['sweet', 'dessert', 'payasam']):
            return 'desserts'
        else:
            return 'general'
    
    def close_connection(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            logger.info("🔌 MongoDB connection closed")

# Global MongoDB service instance
mongodb_service = MongoDBService()
