{% extends 'simple_base.html' %}

{% block title %}{{ page_title }} - Recipe Recommender{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Log Cooking Activity</h2>
                <a href="{% url 'recipe_detail' recipe.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Recipe
                </a>
            </div>

            <!-- Recipe Information -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">{{ recipe.title }}</h5>
                    <p class="card-text text-muted">{{ recipe.description|truncatewords:30 }}</p>
                    <div class="d-flex gap-3">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> {{ recipe.cooking_time }} minutes
                        </small>
                        <small class="text-muted">
                            <i class="fas fa-signal"></i> {{ recipe.get_difficulty_display }}
                        </small>
                        <small class="text-muted">
                            <i class="fas fa-leaf"></i> {{ recipe.get_dietary_restrictions_display }}
                        </small>
                    </div>
                </div>
            </div>

            <!-- Success Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Activity Form -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-4">Share Your Cooking Experience</h5>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Activity Type -->
                        <div class="mb-4">
                            <label for="{{ form.activity_type.id_for_label }}" class="form-label">
                                {{ form.activity_type.label }}
                            </label>
                            {{ form.activity_type }}
                            <div class="form-text">{{ form.activity_type.help_text }}</div>
                            {% if form.activity_type.errors %}
                                <div class="text-danger small">
                                    {{ form.activity_type.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Experience Notes -->
                        <div class="mb-4">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                {{ form.notes.label }}
                            </label>
                            {{ form.notes }}
                            <div class="form-text">{{ form.notes.help_text }}</div>
                            {% if form.notes.errors %}
                                <div class="text-danger small">
                                    {{ form.notes.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Activity Type Guide -->
                        <div class="mb-4">
                            <h6 class="text-muted">Activity Types:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled small">
                                        <li><strong>Recipe View:</strong> Browsed the recipe details</li>
                                        <li><strong>Recipe Rating:</strong> Rated the recipe</li>
                                        <li><strong>Added to Favorites:</strong> Saved for later cooking</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled small">
                                        <li><strong>Recipe Review:</strong> Left a detailed review</li>
                                        <li><strong>Cooked Recipe:</strong> Actually prepared the dish</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Cooking Tips -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-lightbulb text-warning"></i> Share Your Tips
                                </h6>
                                <p class="card-text small mb-0">
                                    Help other home cooks by sharing your experience! Did you make any modifications? 
                                    How did the dish turn out? Any tips for getting the best results? Your insights 
                                    help build our South Indian cooking community.
                                </p>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'recipe_detail' recipe.pk %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Log Activity
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Community Guidelines -->
            <div class="card mt-4 border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-users"></i> Community Guidelines
                    </h6>
                    <ul class="mb-0 small">
                        <li>Share honest and constructive feedback about your cooking experience</li>
                        <li>Include specific details about modifications or substitutions you made</li>
                        <li>Mention any challenges you faced and how you overcame them</li>
                        <li>Be respectful and helpful to fellow South Indian cuisine enthusiasts</li>
                        <li>Focus on authentic cooking techniques and traditional flavors</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS for alerts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}
