# Recipe Recommender Platform - Complete Setup Guide

## Overview
This guide will help you set up the Recipe Recommender Platform, a comprehensive culinary discovery platform focused on authentic South Indian cuisine with advanced analytics and personalized recommendations.

## Prerequisites
- Python 3.8 or higher
- MongoDB (for analytics and advanced search)
- Git (for version control)

## Quick Start

### 1. Environment Setup
```bash
# Create and activate virtual environment
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# Install all dependencies
pip install -r requirements.txt
```

### 2. Database Configuration
```bash
# Run Django migrations for SQLite
python manage.py makemigrations
python manage.py migrate

# Test MongoDB connection
python test_mongodb_connection.py
```

### 3. Initial Data Setup
```bash
# Create superuser account
python manage.py createsuperuser

# Populate South Indian recipes
python add_more_south_indian_recipes.py

# Generate sample analytics data
python manage.py populate_analytics --activities 100
```

### 4. Launch Application
```bash
# Start the development server
python manage.py runserver
```

## 🎯 Testing Platform Features

### 1. Recipe Discovery
1. **Browse South Indian Recipes**: Navigate to the home page to explore featured recipes
2. **Advanced Search**: Use filters for dietary restrictions, cooking time, and difficulty
3. **Category Navigation**: Browse by traditional categories (Rice Dishes, Curries, Snacks, Sweets)

### 2. User Engagement
1. **Recipe Rating**: Rate recipes from 1-5 stars after viewing
2. **Reviews**: Leave detailed cooking experiences and tips
3. **Favorites**: Save recipes to your personal collection
4. **User Profile**: Manage dietary preferences and cooking history

### 3. Analytics Dashboard
1. Login as any user
2. Click on your username dropdown
3. Select "My Analytics"
4. View personalized cooking insights and recipe recommendations

### 2. Admin Analytics Dashboard
1. Login as a staff user (superuser)
2. Click on your username dropdown
3. Select "Admin Dashboard"
4. Explore comprehensive analytics

### 3. Recipe Analytics
1. Login as a staff user
2. Go to any recipe detail page
3. Click the "Analytics" button
4. View detailed recipe performance

### 4. Enhanced Recipe Tracking
1. Browse recipes (automatically tracked)
2. Like/unlike recipes (tracked)
3. Search for recipes (tracked)
4. View your activity in "My Analytics"

## 🔧 Configuration Options

### MongoDB Setup (Production)
1. Install MongoDB locally or use MongoDB Atlas
2. Install pymongo: `pip install pymongo`
3. Update settings.py:
```python
MONGODB_SETTINGS = {
    'host': 'mongodb://your-mongodb-url:27017/recipe_recommender_analytics',
    'connect': False,
}
```

### Analytics Settings
In `settings.py`, you can configure:
- `MONGODB_SETTINGS`: MongoDB connection
- `CACHES`: Cache configuration
- `LOGGING`: Analytics logging

## 📊 Available Analytics

### User Analytics
- Total activities count
- Recipes viewed
- Recipes liked
- Reviews written
- Favorite categories
- Recent activity timeline

### Admin Dashboard
- Total recipes, users, views, searches
- Activity over time charts
- Popular recipes ranking
- Popular search queries
- Category popularity
- Recent activity feed

### Recipe Analytics
- Total and unique views
- Likes and favorites count
- Average rating
- Popularity score
- Activity timeline
- User engagement patterns

## 🚀 Key Features

### 1. Real-time Tracking
- Every recipe view is tracked
- Search queries are logged
- User interactions are recorded
- Analytics update automatically

### 2. MongoDB Integration
- Advanced analytics storage
- User behavior patterns
- Search intelligence
- Recommendation caching

### 3. Visual Analytics
- Interactive charts and graphs
- Responsive design
- Real-time data updates
- Export capabilities

### 4. API Endpoints
- `/analytics/api/recipe/<id>/stats/`
- `/analytics/api/popular-recipes/`
- `/analytics/api/search-suggestions/`

## 🔍 Troubleshooting

### Common Issues

1. **Analytics not showing**
   - Run: `python manage.py populate_analytics`
   - Check if migrations are applied
   - Verify user permissions

2. **MongoDB connection errors**
   - Check MongoDB service is running
   - Verify connection string
   - Install pymongo if using real MongoDB

3. **Analytics not displaying**
   - Check MongoDB connection status
   - Verify Python analytics calculations in views
   - Ensure proper Django template inheritance

### Debug Commands
```bash
# Check analytics data
python manage.py shell
>>> from analytics.models import UserActivity
>>> UserActivity.objects.count()

# Verify MongoDB connection
>>> from analytics.mongodb_client import mongodb_analytics
>>> mongodb_analytics.db  # Should show database object
```

## 📈 Performance Tips

1. **Database Optimization**
   - Regular cleanup of old analytics data
   - Proper indexing (already configured)
   - Monitor query performance

2. **Caching**
   - Enable Redis for production caching
   - Cache popular queries
   - Use CDN for static assets

3. **MongoDB Optimization**
   - Use proper MongoDB indexes
   - Regular data archiving
   - Monitor collection sizes

## 🎨 Customization

### Adding New Analytics
1. Create new activity types in `UserActivity.activity_type`
2. Add tracking in appropriate views
3. Update dashboard templates
4. Create new visualization charts

### Custom Dashboards
1. Extend analytics views
2. Create new templates
3. Add URL patterns
4. Implement custom charts

## 📝 Next Steps

1. **Explore the Analytics**: Browse through all the new analytics features
2. **Generate More Data**: Use the app to create more analytics data
3. **Customize Views**: Modify templates to match your preferences
4. **Add MongoDB**: Set up real MongoDB for production use
5. **Extend Features**: Add more analytics tracking as needed

## 🎯 Success Metrics

After setup, you should see:
- ✅ User analytics dashboard working
- ✅ Admin analytics dashboard accessible
- ✅ Recipe view tracking functional
- ✅ Search analytics recording
- ✅ Charts and visualizations loading
- ✅ MongoDB mock client working

---

**Congratulations!** Your Recipe Recommender now has comprehensive analytics and MongoDB integration. The system tracks user behavior, provides insights, and offers a foundation for advanced recommendation algorithms.
