# Generated by Django 3.1.12 on 2025-05-31 20:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('recipes', '0013_auto_20250531_2216'),
    ]

    operations = [
        migrations.AlterField(
            model_name='recipe',
            name='author',
            field=models.ForeignKey(blank=True, help_text='Recipe contributor', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='cooking_time',
            field=models.PositiveIntegerField(help_text='Active cooking time in minutes'),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='cooking_tips',
            field=models.TextField(blank=True, help_text='Traditional cooking tips and variations'),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='description',
            field=models.TextField(blank=True, help_text='Brief description highlighting South Indian authenticity'),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='dietary_restrictions',
            field=models.CharField(choices=[('none', 'No Restrictions'), ('vegetarian', 'Vegetarian'), ('vegan', 'Vegan'), ('gluten_free', 'Gluten Free'), ('dairy_free', 'Dairy Free')], default='none', help_text='Dietary accommodation for diverse needs', max_length=20),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='difficulty',
            field=models.CharField(choices=[('easy', 'Easy'), ('medium', 'Medium'), ('hard', 'Hard')], default='easy', help_text='Cooking complexity level', max_length=10),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='ingredients',
            field=models.TextField(help_text='Detailed ingredient list with traditional measurements'),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='instructions',
            field=models.TextField(help_text='Step-by-step cooking instructions with traditional techniques'),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='nutritional_info',
            field=models.TextField(blank=True, help_text='Health benefits and nutritional value'),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='rating_average',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Average user rating (1-5 stars)', max_digits=3),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='rating_count',
            field=models.PositiveIntegerField(default=0, help_text='Total number of ratings'),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='title',
            field=models.CharField(help_text='Recipe name (e.g., "Authentic Sambar")', max_length=200),
        ),
        migrations.AlterField(
            model_name='recipe',
            name='view_count',
            field=models.PositiveIntegerField(default=0, help_text='Recipe popularity tracking'),
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bio', models.TextField(blank=True, help_text='User biography and cooking interests', max_length=500)),
                ('cooking_skill', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced'), ('expert', 'Expert')], default='beginner', help_text='Self-assessed cooking skill level', max_length=20)),
                ('preferred_dietary', models.CharField(choices=[('none', 'No Restrictions'), ('vegetarian', 'Vegetarian'), ('vegan', 'Vegan'), ('gluten_free', 'Gluten Free'), ('dairy_free', 'Dairy Free')], default='none', help_text='Primary dietary preference', max_length=20)),
                ('location', models.CharField(blank=True, help_text='User location (optional)', max_length=100)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('last_active', models.DateTimeField(auto_now=True)),
                ('total_recipes_tried', models.PositiveIntegerField(default=0, help_text='Number of recipes user has tried')),
                ('total_recipes_contributed', models.PositiveIntegerField(default=0, help_text='Number of recipes user has added')),
                ('favorite_recipes', models.ManyToManyField(blank=True, help_text="User's favorite recipes", to='recipes.Recipe')),
                ('user', models.OneToOneField(help_text='Associated user account', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
            },
        ),
        migrations.CreateModel(
            name='CookingActivity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('view', 'Recipe View'), ('rate', 'Recipe Rating'), ('favorite', 'Added to Favorites'), ('review', 'Recipe Review'), ('cook', 'Cooked Recipe')], help_text='Type of activity performed', max_length=20)),
                ('timestamp', models.DateTimeField(auto_now_add=True, help_text='When the activity occurred')),
                ('notes', models.TextField(blank=True, help_text='Optional notes about the activity')),
                ('recipe', models.ForeignKey(help_text='Recipe involved in the activity', on_delete=django.db.models.deletion.CASCADE, to='recipes.recipe')),
                ('user', models.ForeignKey(help_text='User who performed the activity', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Cooking Activity',
                'verbose_name_plural': 'Cooking Activities',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='cookingactivity',
            index=models.Index(fields=['user', 'activity_type'], name='user_activity_idx'),
        ),
        migrations.AddIndex(
            model_name='cookingactivity',
            index=models.Index(fields=['recipe', 'activity_type'], name='recipe_activity_idx'),
        ),
    ]
