{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}{{ block.super }}<link rel="stylesheet" href="{% static "admin/css/forms.css" %}">{% endblock %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; {% translate 'Password reset confirmation' %}
</div>
{% endblock %}

{% block content %}

{% if validlink %}

<p>{% translate "Please enter your new password twice so we can verify you typed it in correctly." %}</p>

<form method="post">{% csrf_token %}
<fieldset class="module aligned">
    <input class="hidden" autocomplete="username" value="{{ form.user.get_username }}">
    <div class="form-row field-password1">
        {{ form.new_password1.errors }}
        <label for="id_new_password1">{% translate 'New password:' %}</label>
        {{ form.new_password1 }}
    </div>
    <div class="form-row field-password2">
        {{ form.new_password2.errors }}
        <label for="id_new_password2">{% translate 'Confirm password:' %}</label>
        {{ form.new_password2 }}
    </div>
    <input type="submit" value="{% translate 'Change my password' %}">
</fieldset>
</form>

{% else %}

<p>{% translate "The password reset link was invalid, possibly because it has already been used.  Please request a new password reset." %}</p>

{% endif %}

{% endblock %}
