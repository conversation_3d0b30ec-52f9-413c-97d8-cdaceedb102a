{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h1>{{ recipe.title }}</h1>

            <!-- Enhanced Recipe Info -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Recipe Info</h6>
                            <p class="mb-1"><strong>Prep:</strong> {{ recipe.prep_time }} min</p>
                            <p class="mb-1"><strong>Cook:</strong> {{ recipe.cooking_time }} min</p>
                            <p class="mb-1"><strong>Total:</strong> {{ recipe.get_total_time }} min</p>
                            <p class="mb-1"><strong>Serves:</strong> {{ recipe.servings }}</p>
                            <p class="mb-0"><strong>Views:</strong> {{ recipe.view_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Ratings & Tags</h6>
                            <!-- Rating Display -->
                            {% if recipe.rating_count > 0 %}
                            <p class="mb-1">
                                <span class="text-warning">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= recipe.rating_average %}★{% else %}☆{% endif %}
                                    {% endfor %}
                                </span>
                                <strong>{{ recipe.rating_average }}/5</strong> ({{ recipe.rating_count }} reviews)
                            </p>
                            {% else %}
                            <p class="mb-1 text-muted">No ratings yet</p>
                            {% endif %}

                            <!-- Enhanced Badges -->
                            <div class="mt-2">
                                <span class="badge bg-{{ recipe.get_difficulty_color }} me-1">{{ recipe.get_difficulty_display }}</span>
                                {% if recipe.dietary_restrictions != 'none' %}
                                <span class="badge bg-{{ recipe.get_dietary_display_badge }} me-1">{{ recipe.get_dietary_restrictions_display }}</span>
                                {% endif %}
                                {% if recipe.is_quick_recipe %}
                                <span class="badge bg-success me-1">⚡ Quick</span>
                                {% endif %}
                                {% if recipe.is_popular %}
                                <span class="badge bg-warning text-dark me-1">🔥 Popular</span>
                                {% endif %}
                                {% if recipe.get_season_tag %}
                                <span class="badge bg-info me-1">{{ recipe.get_season_tag }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>Description</h3>
            <p>{{ recipe.description }}</p>

            <h3>Ingredients</h3>
            <div class="ingredients">
                {{ recipe.ingredients|linebreaks }}
            </div>

            <h3>Instructions</h3>
            <div class="instructions">
                {{ recipe.instructions|linebreaks }}
            </div>

            <!-- Cooking Tips Section -->
            {% if recipe.cooking_tips %}
            <div class="alert alert-info mt-4">
                <h5>Cooking Tips</h5>
                <p class="mb-0">{{ recipe.cooking_tips }}</p>
            </div>
            {% endif %}

            <!-- Nutritional Information -->
            {% if recipe.nutritional_info %}
            <div class="alert alert-success mt-3">
                <h5>Nutritional Benefits</h5>
                <p class="mb-0">{{ recipe.nutritional_info }}</p>
            </div>
            {% endif %}

            <!-- User Actions -->
            {% if user.is_authenticated %}
            <div class="d-flex gap-2 mt-4 mb-3">
                <a href="{% url 'add_to_favorites' recipe.pk %}" class="btn btn-outline-danger">
                    <i class="fas fa-heart"></i> Add to Favorites
                </a>
                <a href="{% url 'cooking_activity_log' recipe.pk %}" class="btn btn-outline-success">
                    <i class="fas fa-utensils"></i> Log Cooking Activity
                </a>
            </div>
            {% endif %}

            {% if recipe.author %}
            <p class="text-muted mt-4">
                <small>Recipe by {{ recipe.author.username }} on {{ recipe.created_at|date:"M d, Y" }}</small>
            </p>
            {% endif %}
        </div>

        <div class="col-md-4">
            <!-- Recipe Summary -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recipe Summary</h5>
                </div>
                <div class="card-body">
                    <p><strong>Category:</strong> South Indian</p>
                    <p><strong>Difficulty:</strong> {{ recipe.get_difficulty_display }}</p>
                    <p><strong>Dietary:</strong> {{ recipe.get_dietary_restrictions_display }}</p>
                    {% if recipe.rating_count > 0 %}
                    <p><strong>Rating:</strong>
                        <span class="text-warning">
                            {% for i in "12345" %}
                                {% if forloop.counter <= recipe.rating_average %}★{% else %}☆{% endif %}
                            {% endfor %}
                            ({{ recipe.rating_average }}/5)
                        </span>
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
