from django.db import models
from django.contrib.auth.models import User

class Recipe(models.Model):
    DIETARY_CHOICES = [
        ('none', 'No Restrictions'),
        ('vegetarian', 'Vegetarian'),
        ('vegan', 'Vegan'),
        ('gluten_free', 'Gluten Free'),
        ('dairy_free', 'Dairy Free'),
    ]

    DIFFICULTY_CHOICES = [
        ('easy', 'Easy'),
        ('medium', 'Medium'),
        ('hard', 'Hard'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, help_text='Brief description of the recipe')
    ingredients = models.TextField()
    instructions = models.TextField()
    cooking_time = models.PositiveIntegerField(help_text='Minutes')
    dietary_restrictions = models.CharField(max_length=20, choices=DIETARY_CHOICES, default='none')
    difficulty = models.CharField(max_length=10, choices=DIFFICULTY_CHOICES, default='easy')
    servings = models.PositiveIntegerField(default=4, help_text='Number of people served')
    author = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)

    # Enhanced features
    view_count = models.PositiveIntegerField(default=0, help_text='Number of times viewed')
    cooking_tips = models.TextField(blank=True, help_text='Helpful cooking tips')
    nutritional_info = models.TextField(blank=True, help_text='Nutritional benefits')
    prep_time = models.PositiveIntegerField(default=0, help_text='Preparation time in minutes')
    total_time = models.PositiveIntegerField(default=0, help_text='Total time including prep and cooking')
    rating_average = models.DecimalField(max_digits=3, decimal_places=2, default=0.0)
    rating_count = models.PositiveIntegerField(default=0)

    def __str__(self):
        return self.title

    def get_dietary_display_badge(self):
        """Return a colored badge for dietary restrictions"""
        badges = {
            'vegetarian': 'success',
            'vegan': 'primary',
            'gluten_free': 'warning',
            'dairy_free': 'info',
            'none': 'secondary'
        }
        return badges.get(self.dietary_restrictions, 'secondary')

    def increment_view_count(self):
        """Increment the view count for this recipe"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def get_total_time(self):
        """Calculate total time (prep + cooking)"""
        return self.prep_time + self.cooking_time

    def get_difficulty_color(self):
        """Return color for difficulty level"""
        colors = {
            'easy': 'success',
            'medium': 'warning',
            'hard': 'danger'
        }
        return colors.get(self.difficulty, 'secondary')

    def get_rating_stars(self):
        """Return star rating display"""
        if self.rating_count == 0:
            return "No ratings yet"
        stars = "★" * int(self.rating_average)
        stars += "☆" * (5 - int(self.rating_average))
        return f"{stars} ({self.rating_average:.1f}/5.0 from {self.rating_count} ratings)"

    def is_quick_recipe(self):
        """Check if recipe is quick (≤20 minutes total)"""
        return self.get_total_time() <= 20

    def is_popular(self):
        """Check if recipe is popular (high view count)"""
        return self.view_count >= 50

    def get_season_tag(self):
        """Get seasonal recommendation tag"""
        title_lower = self.title.lower()
        if any(word in title_lower for word in ['thayir', 'mor', 'coconut rice', 'kosambari']):
            return 'Summer Special'
        elif any(word in title_lower for word in ['rasam', 'bonda', 'kesari', 'adai']):
            return 'Monsoon Comfort'
        elif any(word in title_lower for word in ['ragi', 'pongal', 'payasam', 'bisi bele']):
            return 'Winter Warmth'
        elif any(word in title_lower for word in ['mysore pak', 'holige', 'obbattu']):
            return 'Festival Special'
        return None
