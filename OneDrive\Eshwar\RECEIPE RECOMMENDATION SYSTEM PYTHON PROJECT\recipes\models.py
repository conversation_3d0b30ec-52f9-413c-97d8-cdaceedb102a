"""
Recipe Recommender Platform - Core Models

This module defines the core data models for the Recipe Recommender Platform,
a comprehensive culinary discovery platform focused on authentic South Indian cuisine.
The models support advanced recipe management, dietary accommodation, user engagement,
and analytics tracking.

Key Features:
- Comprehensive recipe information with cooking details
- Dietary restriction support (vegetarian, vegan, gluten-free, dairy-free)
- User engagement tracking (views, ratings, reviews)
- South Indian cuisine focus with traditional categorization
- Integration with MongoDB for advanced analytics
"""

from django.db import models
from django.contrib.auth.models import User

class Recipe(models.Model):
    """
    Core Recipe model for South Indian cuisine recipes.

    Stores comprehensive recipe information including ingredients, instructions,
    cooking details, dietary restrictions, and user engagement metrics.
    Designed specifically for authentic South Indian recipes with traditional
    cooking methods and regional variations.
    """
    # Dietary Restriction Choices - Supporting diverse dietary needs
    DIETARY_CHOICES = [
        ('none', 'No Restrictions'),
        ('vegetarian', 'Vegetarian'),      # Traditional South Indian vegetarian
        ('vegan', 'Vegan'),                # Plant-based South Indian recipes
        ('gluten_free', 'Gluten Free'),    # Rice-based and millet recipes
        ('dairy_free', 'Dairy Free'),      # Coconut milk based alternatives
    ]

    # Cooking Difficulty Levels - Based on traditional cooking complexity
    DIFFICULTY_CHOICES = [
        ('easy', 'Easy'),          # Simple everyday recipes (15-30 min)
        ('medium', 'Medium'),      # Traditional recipes requiring technique
        ('hard', 'Hard'),          # Complex festival/special occasion recipes
    ]

    # Core Recipe Information
    title = models.CharField(max_length=200, help_text='Recipe name (e.g., "Authentic Sambar")')
    description = models.TextField(blank=True, help_text='Brief description highlighting South Indian authenticity')
    ingredients = models.TextField(help_text='Detailed ingredient list with traditional measurements')
    instructions = models.TextField(help_text='Step-by-step cooking instructions with traditional techniques')

    # Cooking Details
    cooking_time = models.PositiveIntegerField(help_text='Active cooking time in minutes')
    prep_time = models.PositiveIntegerField(default=0, help_text='Preparation time in minutes')
    total_time = models.PositiveIntegerField(default=0, help_text='Total time including prep and cooking')
    servings = models.PositiveIntegerField(default=4, help_text='Number of people served')

    # Recipe Classification
    dietary_restrictions = models.CharField(max_length=20, choices=DIETARY_CHOICES, default='none',
                                          help_text='Dietary accommodation for diverse needs')
    difficulty = models.CharField(max_length=10, choices=DIFFICULTY_CHOICES, default='easy',
                                help_text='Cooking complexity level')

    # User and Metadata
    author = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True,
                             help_text='Recipe contributor')
    created_at = models.DateTimeField(auto_now_add=True, null=True)

    # Enhanced South Indian Recipe Features
    cooking_tips = models.TextField(blank=True, help_text='Traditional cooking tips and variations')
    nutritional_info = models.TextField(blank=True, help_text='Health benefits and nutritional value')

    # User Engagement and Analytics
    view_count = models.PositiveIntegerField(default=0, help_text='Recipe popularity tracking')
    rating_average = models.DecimalField(max_digits=3, decimal_places=2, default=0.0,
                                       help_text='Average user rating (1-5 stars)')
    rating_count = models.PositiveIntegerField(default=0, help_text='Total number of ratings')

    def __str__(self):
        return self.title

    def increment_view_count(self):
        """Increment the view count for this recipe"""
        self.view_count += 1
        self.save(update_fields=['view_count'])

    def get_total_time(self):
        """Get total cooking time including prep time"""
        return self.prep_time + self.cooking_time

    def is_quick_recipe(self):
        """Check if this is a quick recipe (30 minutes or less)"""
        return self.cooking_time <= 30

    def is_popular(self):
        """Check if this recipe is popular (high view count)"""
        return self.view_count >= 100

    def get_difficulty_color(self):
        """Get Bootstrap color class for difficulty level"""
        colors = {
            'easy': 'success',
            'medium': 'warning',
            'hard': 'danger'
        }
        return colors.get(self.difficulty, 'secondary')

    def get_dietary_display_badge(self):
        """Get Bootstrap color class for dietary restrictions"""
        colors = {
            'vegetarian': 'success',
            'vegan': 'info',
            'gluten_free': 'warning',
            'dairy_free': 'primary',
            'none': 'secondary'
        }
        return colors.get(self.dietary_restrictions, 'secondary')

    def get_season_tag(self):
        """Get seasonal recommendation tag based on recipe name"""
        title_lower = self.title.lower()
        if any(word in title_lower for word in ['thayir', 'mor', 'coconut rice', 'kosambari']):
            return 'Summer Special'
        elif any(word in title_lower for word in ['rasam', 'bonda', 'kesari', 'adai']):
            return 'Monsoon Comfort'
        elif any(word in title_lower for word in ['ragi', 'pongal', 'payasam', 'bisi bele']):
            return 'Winter Warmth'
        elif any(word in title_lower for word in ['mysore pak', 'holige', 'obbattu']):
            return 'Festival Special'
        return None

    def get_rating_stars(self):
        """Return star rating display"""
        if self.rating_count == 0:
            return "No ratings yet"
        stars = "★" * int(self.rating_average)
        stars += "☆" * (5 - int(self.rating_average))
        return f"{stars} ({self.rating_average:.1f}/5.0 from {self.rating_count} ratings)"


class UserProfile(models.Model):
    """
    Extended user profile for Recipe Recommender Platform.

    Stores additional user information including dietary preferences,
    cooking skill level, and personalized settings for South Indian cuisine.
    """
    SKILL_CHOICES = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE,
                               help_text='Associated user account')
    bio = models.TextField(max_length=500, blank=True,
                          help_text='User biography and cooking interests')
    cooking_skill = models.CharField(max_length=20, choices=SKILL_CHOICES,
                                   default='beginner',
                                   help_text='Self-assessed cooking skill level')
    preferred_dietary = models.CharField(max_length=20, choices=Recipe.DIETARY_CHOICES,
                                       default='none',
                                       help_text='Primary dietary preference')
    favorite_recipes = models.ManyToManyField(Recipe, blank=True,
                                            help_text='User\'s favorite recipes')
    location = models.CharField(max_length=100, blank=True,
                              help_text='User location (optional)')
    date_joined = models.DateTimeField(auto_now_add=True)
    last_active = models.DateTimeField(auto_now=True)

    # Cooking statistics
    total_recipes_tried = models.PositiveIntegerField(default=0,
                                                    help_text='Number of recipes user has tried')
    total_recipes_contributed = models.PositiveIntegerField(default=0,
                                                          help_text='Number of recipes user has added')

    class Meta:
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return f"{self.user.username}'s Profile"

    def get_cooking_level_display_color(self):
        """Return CSS color class for cooking skill level"""
        colors = {
            'beginner': 'success',
            'intermediate': 'info',
            'advanced': 'warning',
            'expert': 'danger'
        }
        return colors.get(self.cooking_skill, 'secondary')

    def get_favorite_count(self):
        """Get count of favorite recipes"""
        return self.favorite_recipes.count()

    def get_recent_favorites(self, limit=5):
        """Get recently added favorite recipes"""
        return self.favorite_recipes.all()[:limit]


class CookingActivity(models.Model):
    """
    Track user cooking activities and interactions.

    Records when users view, rate, or interact with recipes
    for analytics and personalized recommendations.
    """
    ACTIVITY_TYPES = [
        ('view', 'Recipe View'),
        ('rate', 'Recipe Rating'),
        ('favorite', 'Added to Favorites'),
        ('review', 'Recipe Review'),
        ('cook', 'Cooked Recipe'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE,
                           help_text='User who performed the activity')
    recipe = models.ForeignKey(Recipe, on_delete=models.CASCADE,
                             help_text='Recipe involved in the activity')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES,
                                   help_text='Type of activity performed')
    timestamp = models.DateTimeField(auto_now_add=True,
                                   help_text='When the activity occurred')
    notes = models.TextField(blank=True,
                           help_text='Optional notes about the activity')

    class Meta:
        verbose_name = 'Cooking Activity'
        verbose_name_plural = 'Cooking Activities'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'activity_type'], name='user_activity_idx'),
            models.Index(fields=['recipe', 'activity_type'], name='recipe_activity_idx'),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_activity_type_display()} - {self.recipe.title}"
