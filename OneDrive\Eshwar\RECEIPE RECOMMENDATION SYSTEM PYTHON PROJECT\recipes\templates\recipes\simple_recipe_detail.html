{% extends 'simple_base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h1>{{ recipe.title }}</h1>

            <!-- Clean design - no images -->

            <!-- Recipe Info Badges -->
            <div class="mb-3">
                <span class="badge bg-{{ recipe.get_dietary_display_badge }} me-2">{{ recipe.get_dietary_restrictions_display }}</span>
                <span class="badge bg-secondary me-2">{{ recipe.get_difficulty_display }}</span>
                <span class="badge bg-info">{{ recipe.servings }} servings</span>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <h3>Ingredients</h3>
                    <div class="card">
                        <div class="card-body">
                            <pre>{{ recipe.ingredients }}</pre>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h3>Instructions</h3>
                    <div class="card">
                        <div class="card-body">
                            <pre>{{ recipe.instructions }}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Recipe Info</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Cooking Time:</strong><br>
                        <span class="text-primary">{{ recipe.cooking_time }} minutes</span>
                    </div>
                    <div class="mb-3">
                        <strong>Difficulty:</strong><br>
                        <span class="text-secondary">{{ recipe.get_difficulty_display }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Servings:</strong><br>
                        <span class="text-info">{{ recipe.servings }} people</span>
                    </div>
                    <div class="mb-3">
                        <strong>Dietary:</strong><br>
                        <span class="text-{{ recipe.get_dietary_display_badge }}">{{ recipe.get_dietary_restrictions_display }}</span>
                    </div>
                    {% if recipe.author %}
                    <div class="mb-3">
                        <strong>Created by:</strong><br>
                        <span class="text-primary">{{ recipe.author.username }}</span>
                    </div>
                    {% endif %}
                    {% if recipe.created_at %}
                    <div class="mb-3">
                        <strong>Added:</strong><br>
                        <span class="text-muted">{{ recipe.created_at|date:"M d, Y" }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{% url 'recipe_list' %}" class="btn btn-secondary">← Back to Recipes</a>
    </div>
</div>
{% endblock %}
