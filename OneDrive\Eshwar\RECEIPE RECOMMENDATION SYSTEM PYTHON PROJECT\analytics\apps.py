"""
Analytics App Configuration for Recipe Recommender Platform

This app handles user analytics, recipe performance tracking, and engagement metrics
using a Python-only approach with MongoDB integration for advanced analytics.
"""

from django.apps import AppConfig


class AnalyticsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'analytics'
    verbose_name = 'Recipe Analytics'
    
    def ready(self):
        """Initialize analytics when app is ready"""
        pass
