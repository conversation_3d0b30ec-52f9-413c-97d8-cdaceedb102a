# Generated by Django 3.1.12 on 2025-05-31 22:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('recipes', '0014_auto_20250601_0229'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(help_text='Date for these statistics', unique=True)),
                ('active_users', models.PositiveIntegerField(default=0, help_text='Number of active users')),
                ('total_recipe_views', models.PositiveIntegerField(default=0, help_text='Total recipe views')),
                ('total_searches', models.PositiveIntegerField(default=0, help_text='Total searches performed')),
                ('new_recipes', models.PositiveIntegerField(default=0, help_text='New recipes added')),
                ('new_users', models.PositiveIntegerField(default=0, help_text='New user registrations')),
            ],
            options={
                'verbose_name': 'Daily Statistics',
                'verbose_name_plural': 'Daily Statistics',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('view', 'Recipe View'), ('like', 'Recipe Like'), ('search', 'Recipe Search'), ('create', 'Recipe Create'), ('review', 'Recipe Review'), ('favorite', 'Add to Favorites')], help_text='Type of user activity', max_length=20)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now, help_text='When the activity occurred')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='User IP address for analytics', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent string')),
                ('recipe', models.ForeignKey(blank=True, help_text='Recipe associated with the activity', null=True, on_delete=django.db.models.deletion.CASCADE, to='recipes.recipe')),
                ('user', models.ForeignKey(blank=True, help_text='User who performed the activity (null for anonymous)', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Activity',
                'verbose_name_plural': 'User Activities',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SearchAnalytics',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(help_text='Search query entered by user', max_length=200)),
                ('results_count', models.PositiveIntegerField(default=0, help_text='Number of results returned')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now, help_text='When search was performed')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='User IP address', null=True)),
                ('user', models.ForeignKey(blank=True, help_text='User who performed search (null for anonymous)', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Search Analytics',
                'verbose_name_plural': 'Search Analytics',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='RecipeAnalytics',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_views', models.PositiveIntegerField(default=0, help_text='Total number of recipe views')),
                ('unique_views', models.PositiveIntegerField(default=0, help_text='Unique users who viewed recipe')),
                ('total_likes', models.PositiveIntegerField(default=0, help_text='Total number of likes')),
                ('total_favorites', models.PositiveIntegerField(default=0, help_text='Times added to favorites')),
                ('engagement_score', models.FloatField(default=0.0, help_text='Calculated engagement score')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='Last analytics update')),
                ('recipe', models.OneToOneField(help_text='Recipe being analyzed', on_delete=django.db.models.deletion.CASCADE, to='recipes.recipe')),
            ],
            options={
                'verbose_name': 'Recipe Analytics',
                'verbose_name_plural': 'Recipe Analytics',
                'ordering': ['-engagement_score'],
            },
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['user', 'activity_type'], name='analytics_user_activity_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['recipe', 'activity_type'], name='analytics_recipe_activity_idx'),
        ),
        migrations.AddIndex(
            model_name='useractivity',
            index=models.Index(fields=['timestamp'], name='analytics_timestamp_idx'),
        ),
        migrations.AddIndex(
            model_name='searchanalytics',
            index=models.Index(fields=['query'], name='analytics_search_query_idx'),
        ),
        migrations.AddIndex(
            model_name='searchanalytics',
            index=models.Index(fields=['timestamp'], name='analytics_search_timestamp_idx'),
        ),
    ]
