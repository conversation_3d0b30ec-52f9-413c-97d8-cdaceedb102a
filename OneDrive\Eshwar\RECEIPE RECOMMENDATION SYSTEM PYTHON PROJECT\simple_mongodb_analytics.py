#!/usr/bin/env python
"""
Simple MongoDB Analytics for Recipe Recommender Platform

This module provides MongoDB analytics functionality that can be used
independently of Django when there are Django compatibility issues.
Uses pure Python with PyMongo for all analytics operations.
"""

import os
import sys
from datetime import datetime, timedelta
from collections import defaultdict

try:
    from pymongo import MongoClient
    PYMONGO_AVAILABLE = True
except ImportError:
    PYMONGO_AVAILABLE = False
    print("⚠️ PyMongo not available. Install with: pip install pymongo")

class RecipeAnalytics:
    """
    Simple MongoDB analytics for Recipe Recommender Platform
    
    Provides analytics functionality using pure Python and MongoDB
    without Django dependencies.
    """
    
    def __init__(self, host='mongodb://localhost:27017/', db_name='recipe_recommender_db'):
        self.host = host
        self.db_name = db_name
        self.client = None
        self.db = None
        self.connect()
    
    def connect(self):
        """Connect to MongoDB"""
        if not PYMONGO_AVAILABLE:
            print("❌ PyMongo not available")
            return False
        
        try:
            self.client = MongoClient(self.host)
            self.db = self.client[self.db_name]
            
            # Test connection
            self.client.admin.command('ping')
            print(f"✅ Connected to MongoDB: {self.db_name}")
            return True
            
        except Exception as e:
            print(f"❌ MongoDB connection failed: {e}")
            return False
    
    def is_connected(self):
        """Check if connected to MongoDB"""
        return self.client is not None and self.db is not None
    
    def get_recipe_stats(self):
        """Get basic recipe statistics"""
        if not self.is_connected():
            return {}
        
        try:
            recipes_collection = self.db['recipes_recipe']
            
            # Basic counts
            total_recipes = recipes_collection.count_documents({})
            
            # Dietary distribution
            dietary_pipeline = [
                {'$group': {'_id': '$dietary_restrictions', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}}
            ]
            dietary_stats = list(recipes_collection.aggregate(dietary_pipeline))
            
            # Difficulty distribution
            difficulty_pipeline = [
                {'$group': {'_id': '$difficulty', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}}
            ]
            difficulty_stats = list(recipes_collection.aggregate(difficulty_pipeline))
            
            # Average cooking time
            avg_time_pipeline = [
                {'$group': {'_id': None, 'avg_cooking_time': {'$avg': '$cooking_time'}}}
            ]
            avg_time_result = list(recipes_collection.aggregate(avg_time_pipeline))
            avg_cooking_time = avg_time_result[0]['avg_cooking_time'] if avg_time_result else 0
            
            return {
                'total_recipes': total_recipes,
                'dietary_distribution': dietary_stats,
                'difficulty_distribution': difficulty_stats,
                'average_cooking_time': round(avg_cooking_time, 1)
            }
            
        except Exception as e:
            print(f"❌ Error getting recipe stats: {e}")
            return {}
    
    def get_popular_recipes(self, limit=10):
        """Get most popular recipes by view count"""
        if not self.is_connected():
            return []
        
        try:
            recipes_collection = self.db['recipes_recipe']
            
            popular_recipes = list(
                recipes_collection.find()
                .sort('view_count', -1)
                .limit(limit)
            )
            
            return popular_recipes
            
        except Exception as e:
            print(f"❌ Error getting popular recipes: {e}")
            return []
    
    def get_user_activity_stats(self):
        """Get user activity statistics"""
        if not self.is_connected():
            return {}
        
        try:
            activities_collection = self.db['user_activities']
            
            # Total activities
            total_activities = activities_collection.count_documents({})
            
            # Activity type distribution
            activity_pipeline = [
                {'$group': {'_id': '$activity_type', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}}
            ]
            activity_stats = list(activities_collection.aggregate(activity_pipeline))
            
            # Recent activity (last 7 days)
            week_ago = datetime.now() - timedelta(days=7)
            recent_activities = activities_collection.count_documents({
                'timestamp': {'$gte': week_ago.isoformat()}
            })
            
            return {
                'total_activities': total_activities,
                'activity_distribution': activity_stats,
                'recent_activities': recent_activities
            }
            
        except Exception as e:
            print(f"❌ Error getting activity stats: {e}")
            return {}
    
    def get_search_analytics(self):
        """Get search analytics"""
        if not self.is_connected():
            return {}
        
        try:
            search_collection = self.db['search_analytics']
            
            # Total searches
            total_searches = search_collection.count_documents({})
            
            # Popular search terms
            search_pipeline = [
                {'$group': {'_id': '$query', 'count': {'$sum': 1}}},
                {'$sort': {'count': -1}},
                {'$limit': 10}
            ]
            popular_searches = list(search_collection.aggregate(search_pipeline))
            
            return {
                'total_searches': total_searches,
                'popular_searches': popular_searches
            }
            
        except Exception as e:
            print(f"❌ Error getting search analytics: {e}")
            return {}
    
    def track_recipe_view(self, recipe_id, user_id=None):
        """Track a recipe view"""
        if not self.is_connected():
            return False
        
        try:
            # Update recipe view count
            recipes_collection = self.db['recipes_recipe']
            recipes_collection.update_one(
                {'id': recipe_id},
                {'$inc': {'view_count': 1}}
            )
            
            # Log activity
            activities_collection = self.db['user_activities']
            activity = {
                'user_id': user_id,
                'activity_type': 'view',
                'recipe_id': recipe_id,
                'timestamp': datetime.now().isoformat(),
                'ip_address': '127.0.0.1'  # Placeholder
            }
            activities_collection.insert_one(activity)
            
            return True
            
        except Exception as e:
            print(f"❌ Error tracking recipe view: {e}")
            return False
    
    def track_search(self, query, user_id=None, results_count=0):
        """Track a search query"""
        if not self.is_connected():
            return False
        
        try:
            search_collection = self.db['search_analytics']
            search_record = {
                'query': query,
                'user_id': user_id,
                'results_count': results_count,
                'timestamp': datetime.now().isoformat()
            }
            search_collection.insert_one(search_record)
            
            return True
            
        except Exception as e:
            print(f"❌ Error tracking search: {e}")
            return False
    
    def generate_analytics_report(self):
        """Generate a comprehensive analytics report"""
        print("\n📊 Recipe Recommender Platform - Analytics Report")
        print("=" * 60)
        
        # Recipe statistics
        recipe_stats = self.get_recipe_stats()
        if recipe_stats:
            print(f"\n🍛 Recipe Statistics:")
            print(f"   Total Recipes: {recipe_stats['total_recipes']}")
            print(f"   Average Cooking Time: {recipe_stats['average_cooking_time']} minutes")
            
            print(f"\n🥗 Dietary Distribution:")
            for item in recipe_stats['dietary_distribution']:
                dietary_type = item['_id'] or 'None'
                print(f"   {dietary_type.title()}: {item['count']} recipes")
            
            print(f"\n⭐ Difficulty Distribution:")
            for item in recipe_stats['difficulty_distribution']:
                difficulty = item['_id'] or 'Unknown'
                print(f"   {difficulty.title()}: {item['count']} recipes")
        
        # Popular recipes
        popular_recipes = self.get_popular_recipes(5)
        if popular_recipes:
            print(f"\n🔥 Top 5 Popular Recipes:")
            for i, recipe in enumerate(popular_recipes, 1):
                title = recipe.get('title', 'Unknown')
                views = recipe.get('view_count', 0)
                print(f"   {i}. {title} ({views} views)")
        
        # User activity
        activity_stats = self.get_user_activity_stats()
        if activity_stats:
            print(f"\n👥 User Activity:")
            print(f"   Total Activities: {activity_stats['total_activities']}")
            print(f"   Recent Activities (7 days): {activity_stats['recent_activities']}")
            
            if activity_stats['activity_distribution']:
                print(f"\n📈 Activity Types:")
                for item in activity_stats['activity_distribution']:
                    activity_type = item['_id']
                    print(f"   {activity_type.title()}: {item['count']}")
        
        # Search analytics
        search_stats = self.get_search_analytics()
        if search_stats:
            print(f"\n🔍 Search Analytics:")
            print(f"   Total Searches: {search_stats['total_searches']}")
            
            if search_stats['popular_searches']:
                print(f"\n🔥 Popular Search Terms:")
                for i, item in enumerate(search_stats['popular_searches'], 1):
                    query = item['_id']
                    count = item['count']
                    print(f"   {i}. '{query}' ({count} searches)")
        
        print(f"\n✅ Analytics report generated successfully!")
    
    def close(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            print("🔌 MongoDB connection closed")

# Global analytics instance
analytics = RecipeAnalytics()

def main():
    """Main function for testing analytics"""
    if not analytics.is_connected():
        print("❌ Cannot connect to MongoDB. Exiting.")
        return
    
    # Generate analytics report
    analytics.generate_analytics_report()
    
    # Close connection
    analytics.close()

if __name__ == '__main__':
    main()
