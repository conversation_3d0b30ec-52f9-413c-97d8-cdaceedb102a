{% extends 'simple_base.html' %}

{% block title %}{{ page_title }} - Recipe Recommender{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Profile Header -->
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">{{ user.get_full_name|default:user.username }}</h2>
                            <p class="text-muted mb-2">@{{ user.username }}</p>
                            {% if profile.bio %}
                                <p class="mb-3">{{ profile.bio }}</p>
                            {% endif %}
                            <div class="d-flex flex-wrap gap-2">
                                <span class="badge bg-{{ profile.get_cooking_level_display_color }} fs-6">
                                    {{ cooking_stats.cooking_level }}
                                </span>
                                {% if cooking_stats.dietary_preference != 'No Restrictions' %}
                                    <span class="badge bg-info fs-6">{{ cooking_stats.dietary_preference }}</span>
                                {% endif %}
                                {% if profile.location %}
                                    <span class="badge bg-secondary fs-6">📍 {{ profile.location }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'profile_edit' %}" class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> Edit Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cooking Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center bg-primary text-white">
                <div class="card-body">
                    <h3 class="mb-0">{{ cooking_stats.total_favorites }}</h3>
                    <p class="mb-0">Favorite Recipes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <h3 class="mb-0">{{ cooking_stats.total_contributed }}</h3>
                    <p class="mb-0">Recipes Shared</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <h3 class="mb-0">{{ profile.total_recipes_tried }}</h3>
                    <p class="mb-0">Recipes Tried</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <h3 class="mb-0">{{ cooking_stats.total_activities }}</h3>
                    <p class="mb-0">Cooking Activities</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Tabs -->
    <div class="row mt-4">
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="favorites-tab" data-bs-toggle="tab" data-bs-target="#favorites" type="button" role="tab">
                        Favorite Recipes ({{ cooking_stats.total_favorites }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contributed-tab" data-bs-toggle="tab" data-bs-target="#contributed" type="button" role="tab">
                        My Recipes ({{ cooking_stats.total_contributed }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                        Recent Activity
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="recommendations-tab" data-bs-toggle="tab" data-bs-target="#recommendations" type="button" role="tab">
                        Recommended for You
                    </button>
                </li>
            </ul>

            <div class="tab-content mt-3" id="profileTabsContent">
                <!-- Favorite Recipes Tab -->
                <div class="tab-pane fade show active" id="favorites" role="tabpanel">
                    {% if favorite_recipes %}
                        <div class="row">
                            {% for recipe in favorite_recipes %}
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <a href="{% url 'recipe_detail' recipe.pk %}" class="text-decoration-none">
                                                    {{ recipe.title }}
                                                </a>
                                            </h6>
                                            <p class="card-text small text-muted">{{ recipe.description|truncatewords:15 }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock"></i> {{ recipe.cooking_time }} min
                                                </small>
                                                <span class="badge bg-{{ recipe.get_difficulty_display|lower }}">
                                                    {{ recipe.get_difficulty_display }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        {% if cooking_stats.total_favorites > 6 %}
                            <div class="text-center mt-3">
                                <a href="{% url 'recipe_list' %}?favorites=true" class="btn btn-outline-primary">
                                    View All Favorites
                                </a>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No favorite recipes yet</h5>
                            <p class="text-muted">Start exploring South Indian recipes and add them to your favorites!</p>
                            <a href="{% url 'recipe_list' %}" class="btn btn-primary">Browse Recipes</a>
                        </div>
                    {% endif %}
                </div>

                <!-- Contributed Recipes Tab -->
                <div class="tab-pane fade" id="contributed" role="tabpanel">
                    {% if contributed_recipes %}
                        <div class="row">
                            {% for recipe in contributed_recipes %}
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <a href="{% url 'recipe_detail' recipe.pk %}" class="text-decoration-none">
                                                    {{ recipe.title }}
                                                </a>
                                            </h6>
                                            <p class="card-text small text-muted">{{ recipe.description|truncatewords:15 }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-eye"></i> {{ recipe.view_count }} views
                                                </small>
                                                <small class="text-muted">
                                                    <i class="fas fa-star"></i> {{ recipe.rating_average|floatformat:1 }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No recipes shared yet</h5>
                            <p class="text-muted">Share your favorite South Indian recipes with the community!</p>
                            <a href="{% url 'recipe_create' %}" class="btn btn-success">Add Recipe</a>
                        </div>
                    {% endif %}
                </div>

                <!-- Recent Activity Tab -->
                <div class="tab-pane fade" id="activity" role="tabpanel">
                    {% if recent_activities %}
                        <div class="list-group">
                            {% for activity in recent_activities %}
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">
                                            {{ activity.get_activity_type_display }} - 
                                            <a href="{% url 'recipe_detail' activity.recipe.pk %}" class="text-decoration-none">
                                                {{ activity.recipe.title }}
                                            </a>
                                        </h6>
                                        <small class="text-muted">{{ activity.timestamp|timesince }} ago</small>
                                    </div>
                                    {% if activity.notes %}
                                        <p class="mb-1 small">{{ activity.notes }}</p>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No recent activity</h5>
                            <p class="text-muted">Start cooking and rating recipes to see your activity here!</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Recommendations Tab -->
                <div class="tab-pane fade" id="recommendations" role="tabpanel">
                    {% if recommended_recipes %}
                        <div class="row">
                            {% for recipe in recommended_recipes %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <a href="{% url 'recipe_detail' recipe.pk %}" class="text-decoration-none">
                                                    {{ recipe.title }}
                                                </a>
                                            </h6>
                                            <p class="card-text small text-muted">{{ recipe.description|truncatewords:20 }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock"></i> {{ recipe.cooking_time }} min
                                                </small>
                                                <span class="badge bg-info">{{ recipe.get_dietary_restrictions_display }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-lightbulb fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No recommendations available</h5>
                            <p class="text-muted">Update your dietary preferences to get personalized recipe recommendations!</p>
                            <a href="{% url 'profile_edit' %}" class="btn btn-primary">Update Preferences</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS for tabs functionality -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}
