#!/usr/bin/env python3
"""
Create sample analytics data for testing admin filters.
"""

import os
import sys
import django
from datetime import datetime, timedelta
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'recipe_recommender.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
django.setup()

from django.contrib.auth.models import User
from recipes.models import Recipe
from analytics.models import UserActivity, RecipeAnalytics, SearchAnalytics, DailyStats


def create_user_activities():
    """Create sample user activities."""
    print("📊 Creating User Activities...")
    
    # Get users and recipes
    users = list(User.objects.all())
    recipes = list(Recipe.objects.all())
    
    if not users:
        print("❌ No users found. Creating admin user...")
        admin_user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
        users = [admin_user]
    
    if not recipes:
        print("❌ No recipes found. Cannot create activities.")
        return
    
    # Create various activities
    activities = []
    activity_types = ['view', 'like', 'search', 'favorite']
    
    for i in range(50):
        activity = UserActivity.objects.create(
            user=random.choice(users),
            activity_type=random.choice(activity_types),
            recipe=random.choice(recipes) if random.choice([True, False]) else None,
            timestamp=datetime.now() - timedelta(days=random.randint(0, 30))
        )
        activities.append(activity)
    
    print(f"✅ Created {len(activities)} user activities")
    return activities


def create_recipe_analytics():
    """Create sample recipe analytics."""
    print("📈 Creating Recipe Analytics...")
    
    recipes = list(Recipe.objects.all())
    if not recipes:
        print("❌ No recipes found. Cannot create analytics.")
        return
    
    analytics = []
    for recipe in recipes[:20]:  # Create analytics for first 20 recipes
        analytics_obj = RecipeAnalytics.objects.create(
            recipe=recipe,
            total_views=random.randint(10, 500),
            unique_views=random.randint(5, 100),
            total_likes=random.randint(0, 50),
            total_favorites=random.randint(0, 25),
            engagement_score=0.0  # Will be calculated
        )
        analytics_obj.calculate_engagement_score()
        analytics_obj.save()
        analytics.append(analytics_obj)
    
    print(f"✅ Created {len(analytics)} recipe analytics")
    return analytics


def create_search_analytics():
    """Create sample search analytics."""
    print("🔍 Creating Search Analytics...")
    
    users = list(User.objects.all())
    if not users:
        print("❌ No users found.")
        return
    
    # Sample search queries related to South Indian cuisine
    search_queries = [
        'dosa', 'idli', 'sambar', 'rasam', 'vada', 'uttapam',
        'coconut rice', 'mysore pak', 'payasam', 'bisi bele bath',
        'easy recipes', 'vegetarian', 'breakfast', 'south indian',
        'traditional', 'festival recipes', 'quick meals'
    ]
    
    searches = []
    for i in range(30):
        search = SearchAnalytics.objects.create(
            query=random.choice(search_queries),
            user=random.choice(users) if random.choice([True, False]) else None,
            results_count=random.randint(0, 20),
            timestamp=datetime.now() - timedelta(days=random.randint(0, 15))
        )
        searches.append(search)
    
    print(f"✅ Created {len(searches)} search analytics")
    return searches


def create_daily_stats():
    """Create sample daily statistics."""
    print("📅 Creating Daily Statistics...")
    
    stats = []
    for i in range(30):  # Last 30 days
        date = datetime.now().date() - timedelta(days=i)
        daily_stat = DailyStats.objects.create(
            date=date,
            active_users=random.randint(5, 50),
            total_recipe_views=random.randint(20, 200),
            total_searches=random.randint(5, 30),
            new_recipes=random.randint(0, 5),
            new_users=random.randint(0, 3)
        )
        stats.append(daily_stat)
    
    print(f"✅ Created {len(stats)} daily statistics")
    return stats


def test_filters():
    """Test the admin filters with actual data."""
    print("\n🧪 Testing Admin Filters with Real Data")
    print("-" * 50)
    
    # Test difficulty distribution
    print("⚡ Recipe Difficulty Distribution:")
    for difficulty, display in Recipe.DIFFICULTY_CHOICES:
        count = Recipe.objects.filter(difficulty=difficulty).count()
        print(f"   {display}: {count} recipes")
    
    # Test dietary distribution
    print("\n🥗 Recipe Dietary Distribution:")
    for dietary, display in Recipe.DIETARY_CHOICES:
        count = Recipe.objects.filter(dietary_restrictions=dietary).count()
        print(f"   {display}: {count} recipes")
    
    # Test category distribution (based on keywords)
    print("\n🗂️  Recipe Category Distribution:")
    categories = {
        'breakfast': ['dosa', 'idli', 'uttapam', 'upma'],
        'main_course': ['sambar', 'rasam', 'rice'],
        'snacks': ['vada', 'bonda', 'bajji'],
        'desserts': ['payasam', 'mysore pak', 'kesari']
    }
    
    for category, keywords in categories.items():
        count = 0
        for keyword in keywords:
            count += Recipe.objects.filter(title__icontains=keyword).count()
        print(f"   {category.replace('_', ' ').title()}: {count} recipes")
    
    # Test analytics data
    print(f"\n📊 Analytics Data Summary:")
    print(f"   User Activities: {UserActivity.objects.count()}")
    print(f"   Recipe Analytics: {RecipeAnalytics.objects.count()}")
    print(f"   Search Analytics: {SearchAnalytics.objects.count()}")
    print(f"   Daily Statistics: {DailyStats.objects.count()}")


def main():
    """Create all sample analytics data."""
    print("🚀 Creating Sample Analytics Data for Admin Testing")
    print("=" * 60)
    
    # Clear existing analytics data
    print("🧹 Clearing existing analytics data...")
    UserActivity.objects.all().delete()
    RecipeAnalytics.objects.all().delete()
    SearchAnalytics.objects.all().delete()
    DailyStats.objects.all().delete()
    print("✅ Cleared existing data")
    
    # Create new sample data
    create_user_activities()
    create_recipe_analytics()
    create_search_analytics()
    create_daily_stats()
    
    # Test filters
    test_filters()
    
    print("\n🎉 Sample Analytics Data Created Successfully!")
    print("\n🔗 Next Steps:")
    print("   1. Go to http://127.0.0.1:8001/admin/")
    print("   2. Login with admin credentials")
    print("   3. Navigate to Analytics section")
    print("   4. Test the category and difficulty filters!")
    print("   5. Look for filter options in the right sidebar")


if __name__ == "__main__":
    main()
