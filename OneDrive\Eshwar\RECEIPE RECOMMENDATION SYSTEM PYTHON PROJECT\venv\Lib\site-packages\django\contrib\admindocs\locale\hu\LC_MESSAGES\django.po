# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>zentkirályi, 2016
# At<PERSON><PERSON> <>, 2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2011
# János R, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-02 13:45+0000\n"
"Last-Translator: János R\n"
"Language-Team: Hungarian (http://www.transifex.com/django/django/language/"
"hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Adminisztrációs dokumentáció"

msgid "Home"
msgstr "Kezdőlap"

msgid "Documentation"
msgstr "Dokumentáció"

msgid "Bookmarklets"
msgstr "Könyvjelzők"

msgid "Documentation bookmarklets"
msgstr "Dokumentum könyvjelzők"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"A könyvjelzők felvételéhez húzza a könyvjelzők linkjét az eszköztárra, vagy "
"kattintson rájuk jobb egérgombbal és úgy adja hozzá. Ezután már ki tudja "
"választani a könyvjelzőt a honlap bármely oldaláról."

msgid "Documentation for this page"
msgstr "Az oldal dokumentációja"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Bármely oldalról annak a nézetnek a dokumentációjára ugrik, amely a kérdéses "
"oldalt generálta."

msgid "Tags"
msgstr "Címkék"

msgid "List of all the template tags and their functions."
msgstr ""
"Az összes sablon címke (template tags) és a hozzá tartozó függvények listája"

msgid "Filters"
msgstr "Szűrők"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"A szűrők olyan műveletek, amelyeket változókon végrehajtva a sablon kimenete "
"módosul."

msgid "Models"
msgstr "Modellek"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"A modellek a rendszerben található objektumok és a hozzá tartozó mezők "
"leírásai. Minden modell rendelkezik mezőkkel, amelyek a sablonokban "
"változóként érhetők el."

msgid "Views"
msgstr "Nézetek"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"A honlapon minden oldalt egy nézet generál. A nézet definiálja, melyik "
"sablonból készül az oldal, és milyen objektumok állnak az adott nézet "
"rendelkezésére."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Segédeszközök a böngészőjébe az admin funkciók gyors eléréséhez."

msgid "Please install docutils"
msgstr "Kérem telepítse a docutils-t"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Az adminisztrációs dokumentáció működéséhez szükség van a Python <a href="
"\"%(link)s\">docutils</a> könyvtárára."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Kérje meg a rendszergazdát, hogy telepítse a <a href=\"%(link)s\">docutils</"
"a>-t."

#, python-format
msgid "Model: %(name)s"
msgstr "%(name)s modell"

msgid "Fields"
msgstr "Mezők"

msgid "Field"
msgstr "Mező"

msgid "Type"
msgstr "Típus"

msgid "Description"
msgstr "Leírás"

msgid "Methods with arguments"
msgstr "Metódusok és paramétereik"

msgid "Method"
msgstr "Metódus"

msgid "Arguments"
msgstr "Paraméterek"

msgid "Back to Model documentation"
msgstr "Vissza a modell dokumentációhoz"

msgid "Model documentation"
msgstr "Modell dokumentáció"

msgid "Model groups"
msgstr "Modell csoportok"

msgid "Templates"
msgstr "Sablonok"

#, python-format
msgid "Template: %(name)s"
msgstr "%(name)s sablon"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Sablon: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "<q>%(name)s</q>sablon keresési útvonala:"

msgid "(does not exist)"
msgstr "(nem létezik)"

msgid "Back to Documentation"
msgstr "Vissza a dokumentációhoz"

msgid "Template filters"
msgstr "Sablon szűrők"

msgid "Template filter documentation"
msgstr "Sablon szűrő dokumentáci"

msgid "Built-in filters"
msgstr "Beépített szűrők"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Ezen szűrők használatához a <code>%(code)s</code> kódrészletnek kell "
"szerepelnie használat előtt a sablonban."

msgid "Template tags"
msgstr "Sablon címkék"

msgid "Template tag documentation"
msgstr "Sablon címke dokumentáció"

msgid "Built-in tags"
msgstr "Beépített címkék"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Ezen címkék használatához a <code>%(code)s</code> kódrészletnek kell "
"szerepelnie használat előtt a sablonban."

#, python-format
msgid "View: %(name)s"
msgstr "%(name)s nézet"

msgid "Context:"
msgstr "Környezet:"

msgid "Templates:"
msgstr "Sablonok:"

msgid "Back to View documentation"
msgstr "Vissza a nézetek dokumentációhoz"

msgid "View documentation"
msgstr "Nézet dokumentáció"

msgid "Jump to namespace"
msgstr "Ugrás a névtérhez"

msgid "Empty namespace"
msgstr "Üres névtér"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "%(name)s névtérhez tartozó nézetek"

msgid "Views by empty namespace"
msgstr "Névtér nélküli nézetek"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Nézet függvény: <code>%(full_name)s</code>. Név: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "címke:"

msgid "filter:"
msgstr "szűrő:"

msgid "view:"
msgstr "nézet:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "%(app_label)r alkalmazás nem található"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""
"%(model_name)r modell nem található a következő alkalmazásban: %(app_label)r"

msgid "model:"
msgstr "modell:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "a kapcsolódó '%(app_label)s.%(data_type)s' objektum"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "kapcsolódó '%(app_label)s.%(object_name)s' objektumok"

#, python-format
msgid "all %s"
msgstr "minden %s"

#, python-format
msgid "number of %s"
msgstr "%s mennyisége"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nem tűnik egy urlpattern objektumnak."
