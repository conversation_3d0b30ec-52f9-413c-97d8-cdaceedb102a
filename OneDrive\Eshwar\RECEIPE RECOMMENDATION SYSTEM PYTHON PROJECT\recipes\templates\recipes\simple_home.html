{% extends 'simple_base.html' %}

{% block content %}
<div class="container mt-4">
    <h1>Recipe Recommender</h1>
    <p>🌶️ Authentic South Indian cuisine made simple! Discover traditional flavors from Tamil Nadu, Karnataka, Kerala, and Andhra Pradesh.</p>

    {% if user.is_authenticated %}
        <div class="alert alert-success">
            <strong>Welcome back, {{ user.username }}!</strong> Ready to cook something delicious?
        </div>
        <a href="{% url 'recipe_create' %}" class="btn btn-primary mb-4">Add New Recipe</a>
    {% else %}
        <div class="alert alert-info">
            <strong>Welcome to Recipe Recommender!</strong>
            <a href="{% url 'login' %}" class="alert-link">Login</a> or
            <a href="{% url 'signup' %}" class="alert-link">Sign up</a> to share your authentic South Indian recipes!
        </div>
    {% endif %}

    <div class="row">
        {% for recipe in recipes %}
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <!-- Clean design - no images -->
                <div class="card-body">
                    <h5 class="card-title">
                        <a href="{% url 'recipe_detail' recipe.pk %}" class="text-decoration-none">{{ recipe.title }}</a>
                    </h5>
                    <div class="mb-2">
                        <span class="badge bg-{{ recipe.get_dietary_display_badge }}">{{ recipe.get_dietary_restrictions_display }}</span>
                        <span class="badge bg-secondary">{{ recipe.get_difficulty_display }}</span>
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-clock"></i> {{ recipe.cooking_time }} min
                        <span class="mx-2">•</span>
                        <i class="fas fa-users"></i> {{ recipe.servings }} servings
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <p>No recipes yet. <a href="{% url 'recipe_create' %}">Add the first one!</a></p>
        </div>
        {% endfor %}
    </div>

    <div class="mt-4">
        <a href="{% url 'recipe_list' %}" class="btn btn-outline-primary">View All Recipes</a>
    </div>
</div>
{% endblock %}
