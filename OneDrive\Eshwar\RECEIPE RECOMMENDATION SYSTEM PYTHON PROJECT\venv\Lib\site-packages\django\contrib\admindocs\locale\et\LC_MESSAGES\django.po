# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2013,2015
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-11-22 11:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Estonian (http://www.transifex.com/django/django/language/"
"et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administreerimise Dokumentatsioon"

msgid "Home"
msgstr "Kodu"

msgid "Documentation"
msgstr "Dokumentatsioon"

msgid "Bookmarklets"
msgstr "Järjehoidjandid"

msgid "Documentation bookmarklets"
msgstr "Dokumentatsiooni järjehoidjandid"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Järjehoidjandite paigaldamiseks lohista viit järjehoidjateribale või tee "
"parem-klikk viidal ning lisa see järjehoidjatesse. Seejärel võite "
"järjehoidjandi aktiveerida suvaliselt saidi lehelt."

msgid "Documentation for this page"
msgstr "Selle lehekülje dokumentatsioon"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Suunab teid suvalisel lehel asudes selle vaate dokumentatsioonile, mis antud "
"lehe genereerib."

msgid "Tags"
msgstr "Lipikud"

msgid "List of all the template tags and their functions."
msgstr "Nimekiri kõikidest malli märgistustest ja nende funktsioonidest."

msgid "Filters"
msgstr "Filtrid"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtrid on tegevused, mida saab mallides külge panna muutujatele muutmaks "
"viimaste väljundit."

msgid "Models"
msgstr "Mudelid"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Mudelid on süsteemi kõikide objektide ja nende väljade omavaheliste seoste "
"kirjeldused. Igal mudelil on hulk väljasid, mida saab mallide muutujatena "
"kasutada"

msgid "Views"
msgstr "Vaated"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Avaliku saidi iga leht on genereeritud vaate poolt. Vaade defineerib lehe "
"genereerimiseks kasutatava malli ning objektid mida mall kasutada saab."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Töövahendid sinu brauserile pääsemaks kiiresti ligi administraatori "
"funktsioonidele. "

msgid "Please install docutils"
msgstr "Palun paigaldage docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Administreerimise dokumentatsioon vajab Python'i <a href=\"%(link)s"
"\">docutils</a> teeki."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "Palu administraatoril paigaldada <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Mudel: %(name)s"

msgid "Fields"
msgstr "Väljad"

msgid "Field"
msgstr "Väli"

msgid "Type"
msgstr "Tüüp"

msgid "Description"
msgstr "Kirjeldus"

msgid "Methods with arguments"
msgstr "Meetodid parameetritega"

msgid "Method"
msgstr "Meetod"

msgid "Arguments"
msgstr "Parameetrid"

msgid "Back to Model documentation"
msgstr "Tagasi Mudeli dokumentatsiooni"

msgid "Model documentation"
msgstr "Mudeli dokumentatsioon"

msgid "Model groups"
msgstr "Mudeligrupid"

msgid "Templates"
msgstr "Mallid"

#, python-format
msgid "Template: %(name)s"
msgstr "Mall: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Mall: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Otsinguteekond mallile <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(pole olemas)"

msgid "Back to Documentation"
msgstr "Tagasi Dokumentatsiooni"

msgid "Template filters"
msgstr "Mallifiltrid"

msgid "Template filter documentation"
msgstr "Mallifiltrite dokumentatsioon"

msgid "Built-in filters"
msgstr "Sisseehitatud filtrid"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Et kasutada neid filtreid, pane malli sisse <code>%(code)s</code> enne kui "
"kasutad filtrit."

msgid "Template tags"
msgstr "Malli märgendid"

msgid "Template tag documentation"
msgstr "Malli märgendite dokumentatsioon"

msgid "Built-in tags"
msgstr "Sisseehitatud märgendid"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Et kasutada neid märgendeid pane malli <code>%(code)s</code> enne kui "
"kasutad märgendit."

#, python-format
msgid "View: %(name)s"
msgstr "Vaade: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Mallid:"

msgid "Back to View documentation"
msgstr "Tagasi Vaate dokumentatsiooni"

msgid "View documentation"
msgstr "Vaata dokumentatsiooni"

msgid "Jump to namespace"
msgstr "Mine nimeruumile"

msgid "Empty namespace"
msgstr "Tühi nimeruum"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vaated nimeruumi kaupa %(name)s"

msgid "Views by empty namespace"
msgstr "Vaated tühja nimeruumi kaupa"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Vaate funktsioon: <code>%(full_name)s</code>. Nimi: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "lipik:"

msgid "filter:"
msgstr "filtreeri:"

msgid "view:"
msgstr "vaade:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Rakendust %(app_label)r ei leitud"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Mudelit %(model_name)r ei leitud rakendusest %(app_label)r"

msgid "model:"
msgstr "mudel:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "seotud `%(app_label)s.%(data_type)s` objekt"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "seotud `%(app_label)s.%(object_name)s` objektid"

#, python-format
msgid "all %s"
msgstr "kõik %s"

#, python-format
msgid "number of %s"
msgstr "%s arv"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ei tundu olevat urlpattern objekt"
