from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('recipes/', views.recipe_list, name='recipe_list'),
    path('recipe/<int:pk>/', views.recipe_detail, name='recipe_detail'),
    path('recipe/new/', views.recipe_create, name='recipe_create'),

    path('signup/', views.signup, name='signup'),
    path('logout/', views.custom_logout, name='custom_logout'),

    # Profile URLs
    path('profile/', views.profile_view, name='profile_view'),
    path('profile/edit/', views.profile_edit, name='profile_edit'),
    path('recipe/<int:pk>/favorite/', views.add_to_favorites, name='add_to_favorites'),
    path('recipe/<int:pk>/activity/', views.cooking_activity_log, name='cooking_activity_log'),
]