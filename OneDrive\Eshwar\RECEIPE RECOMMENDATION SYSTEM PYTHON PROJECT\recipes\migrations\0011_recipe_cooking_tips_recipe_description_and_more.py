# Generated by Django 5.2.1 on 2025-05-31 14:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('recipes', '0010_recipe_image'),
    ]

    operations = [
        migrations.AddField(
            model_name='recipe',
            name='cooking_tips',
            field=models.TextField(blank=True, help_text='Helpful cooking tips'),
        ),
        migrations.AddField(
            model_name='recipe',
            name='description',
            field=models.TextField(blank=True, help_text='Brief description of the recipe'),
        ),
        migrations.AddField(
            model_name='recipe',
            name='nutritional_info',
            field=models.TextField(blank=True, help_text='Nutritional benefits'),
        ),
        migrations.AddField(
            model_name='recipe',
            name='prep_time',
            field=models.PositiveIntegerField(default=0, help_text='Preparation time in minutes'),
        ),
        migrations.AddField(
            model_name='recipe',
            name='rating_average',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=3),
        ),
        migrations.AddField(
            model_name='recipe',
            name='rating_count',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='recipe',
            name='total_time',
            field=models.PositiveIntegerField(default=0, help_text='Total time including prep and cooking'),
        ),
        migrations.AddField(
            model_name='recipe',
            name='view_count',
            field=models.PositiveIntegerField(default=0, help_text='Number of times viewed'),
        ),
    ]
