{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>All Recipes</h1>
            <p class="text-muted">Discover amazing recipes from our community</p>
        </div>
        <div class="col-md-4 text-end">
            {% if user.is_authenticated %}
            <a href="{% url 'recipe_create' %}" class="btn btn-primary">Add New Recipe</a>
            {% endif %}
        </div>
    </div>

    <!-- Simple Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <input type="text" class="form-control" name="q" value="{{ current_query }}"
                           placeholder="Search recipes...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="category">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="difficulty">
                        <option value="">All Difficulties</option>
                        {% for value, label in difficulties %}
                        <option value="{{ value }}" {% if current_difficulty == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Recipes Grid -->
    <div class="row">
        {% for recipe in recipes %}
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="recipe-placeholder" style="background: linear-gradient(135deg, #ff6b35, #f7931e); color: white;">
                    <div class="text-center py-4">
                        <div class="h4 fw-bold">{{ recipe.title }}</div>
                        <div class="small">{{ recipe.get_difficulty_display }}</div>
                        <div class="small">{{ recipe.cooking_time }} min</div>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ recipe.title }}</h5>
                    <p class="card-text">{{ recipe.description|truncatewords:15 }}</p>

                    <!-- Enhanced Badges -->
                    <div class="mb-2">
                        <!-- Dietary Restrictions Badge -->
                        {% if recipe.dietary_restrictions != 'none' %}
                        <span class="badge bg-{{ recipe.get_dietary_display_badge }} me-1">
                            {{ recipe.get_dietary_restrictions_display }}
                        </span>
                        {% endif %}

                        <!-- Quick Recipe Badge -->
                        {% if recipe.is_quick_recipe %}
                        <span class="badge bg-success me-1">⚡ Quick</span>
                        {% endif %}

                        <!-- Popular Badge -->
                        {% if recipe.is_popular %}
                        <span class="badge bg-warning text-dark me-1">🔥 Popular</span>
                        {% endif %}

                        <!-- Seasonal Badge -->
                        {% if recipe.get_season_tag %}
                        <span class="badge bg-info me-1">{{ recipe.get_season_tag }}</span>
                        {% endif %}
                    </div>

                    <!-- Enhanced Recipe Info -->
                    <div class="d-flex justify-content-between text-muted small mb-2">
                        <span>{{ recipe.get_total_time }} min</span>
                        <span>{{ recipe.servings }} servings</span>
                        <span>{{ recipe.view_count }} views</span>
                    </div>

                    <!-- Rating -->
                    {% if recipe.rating_count > 0 %}
                    <div class="mb-2">
                        <small class="text-warning">
                            {% for i in "12345" %}
                                {% if forloop.counter <= recipe.rating_average %}★{% else %}☆{% endif %}
                            {% endfor %}
                            <span class="text-muted">({{ recipe.rating_average }}/5)</span>
                        </small>
                    </div>
                    {% endif %}

                    <a href="{% url 'recipe_detail' recipe.pk %}" class="btn btn-primary btn-sm">View Recipe</a>
                    {% if recipe.author %}
                    <small class="text-muted d-block mt-2">by {{ recipe.author.username }}</small>
                    {% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12 text-center">
            <p class="text-muted">No recipes found.</p>
            <a href="{% url 'recipe_list' %}" class="btn btn-primary">View All Recipes</a>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Recipe pagination">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
            </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
            </li>

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}