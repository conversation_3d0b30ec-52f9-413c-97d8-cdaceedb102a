#!/usr/bin/env python
"""
Test MongoDB connection
"""
import pymongo
from pymongo import MongoClient

def test_mongodb_connection():
    """Test connection to MongoDB"""
    try:
        # Connect to MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        
        # Test the connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful!")
        
        # List databases
        databases = client.list_database_names()
        print(f"📁 Available databases: {databases}")
        
        # Create/access our database
        db = client['recipe_recommender_db']
        print(f"🗄️  Connected to database: recipe_recommender_db")
        
        # List collections
        collections = db.list_collection_names()
        print(f"📋 Collections in database: {collections}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        print("💡 Make sure MongoDB is running on localhost:27017")
        print("💡 You can start MongoDB Compass and connect to mongodb://localhost:27017")
        return False

if __name__ == '__main__':
    test_mongodb_connection()
