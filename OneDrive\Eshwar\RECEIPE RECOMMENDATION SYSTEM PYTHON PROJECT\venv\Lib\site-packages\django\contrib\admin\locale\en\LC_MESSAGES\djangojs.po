# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: contrib/admin/static/admin/js/SelectFilter2.js:38
#, javascript-format
msgid "Available %s"
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:44
#, javascript-format
msgid ""
"This is the list of available %s. You may choose some by selecting them in "
"the box below and then clicking the \"Choose\" arrow between the two boxes."
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:60
#, javascript-format
msgid "Type into this box to filter down the list of available %s."
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:65
msgid "Filter"
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:69
msgid "Choose all"
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:69
#, javascript-format
msgid "Click to choose all %s at once."
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:75
msgid "Choose"
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:77
msgid "Remove"
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:83
#, javascript-format
msgid "Chosen %s"
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:89
#, javascript-format
msgid ""
"This is the list of chosen %s. You may remove some by selecting them in the "
"box below and then clicking the \"Remove\" arrow between the two boxes."
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:99
msgid "Remove all"
msgstr ""

#: contrib/admin/static/admin/js/SelectFilter2.js:99
#, javascript-format
msgid "Click to remove all chosen %s at once."
msgstr ""

#: contrib/admin/static/admin/js/actions.js:49
#: contrib/admin/static/admin/js/actions.min.js:2
msgid "%(sel)s of %(cnt)s selected"
msgid_plural "%(sel)s of %(cnt)s selected"
msgstr[0] ""
msgstr[1] ""

#: contrib/admin/static/admin/js/actions.js:118
#: contrib/admin/static/admin/js/actions.min.js:5
msgid ""
"You have unsaved changes on individual editable fields. If you run an "
"action, your unsaved changes will be lost."
msgstr ""

#: contrib/admin/static/admin/js/actions.js:130
#: contrib/admin/static/admin/js/actions.min.js:5
msgid ""
"You have selected an action, but you haven’t saved your changes to "
"individual fields yet. Please click OK to save. You’ll need to re-run the "
"action."
msgstr ""

#: contrib/admin/static/admin/js/actions.js:132
#: contrib/admin/static/admin/js/actions.min.js:6
msgid ""
"You have selected an action, and you haven’t made any changes on individual "
"fields. You’re probably looking for the Go button rather than the Save "
"button."
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:13
#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:113
msgid "Now"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:14
msgid "Midnight"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:15
msgid "6 a.m."
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:16
msgid "Noon"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:17
msgid "6 p.m."
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:80
#, javascript-format
msgid "Note: You are %s hour ahead of server time."
msgid_plural "Note: You are %s hours ahead of server time."
msgstr[0] ""
msgstr[1] ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:88
#, javascript-format
msgid "Note: You are %s hour behind server time."
msgid_plural "Note: You are %s hours behind server time."
msgstr[0] ""
msgstr[1] ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:131
msgid "Choose a Time"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:161
msgid "Choose a time"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:178
#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:336
msgid "Cancel"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:241
#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:321
msgid "Today"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:258
msgid "Choose a Date"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:315
msgid "Yesterday"
msgstr ""

#: contrib/admin/static/admin/js/admin/DateTimeShortcuts.js:327
msgid "Tomorrow"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:11
msgid "January"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:12
msgid "February"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:13
msgid "March"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:14
msgid "April"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:15
msgid "May"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:16
msgid "June"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:17
msgid "July"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:18
msgid "August"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:19
msgid "September"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:20
msgid "October"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:21
msgid "November"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:22
msgid "December"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:25
msgctxt "one letter Sunday"
msgid "S"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:26
msgctxt "one letter Monday"
msgid "M"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:27
msgctxt "one letter Tuesday"
msgid "T"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:28
msgctxt "one letter Wednesday"
msgid "W"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:29
msgctxt "one letter Thursday"
msgid "T"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:30
msgctxt "one letter Friday"
msgid "F"
msgstr ""

#: contrib/admin/static/admin/js/calendar.js:31
msgctxt "one letter Saturday"
msgid "S"
msgstr ""

#: contrib/admin/static/admin/js/collapse.js:16
#: contrib/admin/static/admin/js/collapse.js:34
#: contrib/admin/static/admin/js/collapse.min.js:1
#: contrib/admin/static/admin/js/collapse.min.js:2
msgid "Show"
msgstr ""

#: contrib/admin/static/admin/js/collapse.js:30
#: contrib/admin/static/admin/js/collapse.min.js:2
msgid "Hide"
msgstr ""
