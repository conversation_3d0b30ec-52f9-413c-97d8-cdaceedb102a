# Recipe Recommender Platform - Analytics API Documentation

## Overview
The Recipe Recommender Platform provides a comprehensive REST API for accessing recipe analytics, user engagement metrics, and personalized recommendations. This API enables developers to build rich analytics dashboards and integrate recipe data into external applications.

## Base URL
```
http://127.0.0.1:8000/analytics/api/
```

## Authentication
Most endpoints require user authentication for personalized data. Staff-only endpoints require administrative privileges for comprehensive analytics access.

## Endpoints

### 1. Recipe Statistics
**GET** `/recipe/<int:recipe_id>/stats/`

Get comprehensive statistics for a specific recipe.

#### Parameters
- `recipe_id` (int): The ID of the recipe

#### Response
```json
{
    "recipe_id": 1,
    "title": "Authentic Sambar - Traditional South Indian Lentil Curry",
    "total_views": 245,
    "unique_views": 189,
    "total_likes": 67,
    "total_favorites": 34,
    "total_reviews": 23,
    "average_rating": 4.7,
    "popularity_score": 456.8,
    "dietary_restrictions": "vegetarian",
    "difficulty": "medium",
    "cooking_time": 45,
    "category": "Curries",
    "last_updated": "2025-05-28T13:54:56.123456Z"
}
```

#### Example
```bash
curl -X GET "http://127.0.0.1:8000/analytics/api/recipe/1/stats/"
```

### 2. Popular Recipes
**GET** `/popular-recipes/`

Get a list of the most popular recipes based on analytics.

#### Parameters
- `limit` (int, optional): Number of recipes to return (default: 10, max: 50)

#### Response
```json
{
    "recipes": [
        {
            "id": 1,
            "title": "Classic Spaghetti Carbonara",
            "category": "Italian",
            "difficulty": "medium",
            "cooking_time": 30,
            "popularity_score": 156.8,
            "total_views": 45,
            "total_likes": 12,
            "average_rating": 4.2
        },
        {
            "id": 2,
            "title": "Chicken Tikka Masala",
            "category": "Indian",
            "difficulty": "hard",
            "cooking_time": 60,
            "popularity_score": 142.3,
            "total_views": 38,
            "total_likes": 15,
            "average_rating": 4.5
        }
    ]
}
```

#### Example
```bash
curl -X GET "http://127.0.0.1:8000/analytics/api/popular-recipes/?limit=5"
```

### 3. Search Suggestions
**GET** `/search-suggestions/`

Get search suggestions based on popular search queries.

#### Parameters
- `q` (string): Query string to get suggestions for (minimum 2 characters)

#### Response
```json
{
    "suggestions": [
        "pasta recipes",
        "pasta salad",
        "pasta sauce",
        "pasta bake"
    ]
}
```

#### Example
```bash
curl -X GET "http://127.0.0.1:8000/analytics/api/search-suggestions/?q=pas"
```

## Error Responses

### 404 Not Found
```json
{
    "error": "Recipe not found"
}
```

### 400 Bad Request
```json
{
    "error": "Invalid parameters"
}
```

### 403 Forbidden
```json
{
    "error": "Permission denied"
}
```

## Usage Examples

### Python/Django Views
```python
# Get recipe statistics in Django view
from django.shortcuts import render
from django.http import JsonResponse
from recipes.mongodb_service import mongodb_service

def recipe_stats_view(request, recipe_id):
    """Get recipe statistics for display in Django template"""
    stats = mongodb_service.get_recipe_analytics()
    recipe = get_object_or_404(Recipe, id=recipe_id)

    context = {
        'recipe': recipe,
        'total_views': stats.get('total_views', 0),
        'total_likes': stats.get('total_likes', 0),
        'popularity_score': stats.get('popularity_score', 0)
    }
    return render(request, 'recipes/recipe_stats.html', context)

# Get popular recipes for homepage
def popular_recipes_view(request):
    """Display popular South Indian recipes"""
    popular_recipes = mongodb_service.get_popular_recipes(limit=5)

    context = {
        'popular_recipes': popular_recipes,
        'page_title': 'Popular South Indian Recipes'
    }
    return render(request, 'recipes/popular_recipes.html', context)
```

### Python/Requests
```python
import requests

# Get recipe statistics
response = requests.get('http://127.0.0.1:8000/analytics/api/recipe/1/stats/')
if response.status_code == 200:
    stats = response.json()
    print(f"Recipe has {stats['total_views']} views")

# Get popular recipes
response = requests.get('http://127.0.0.1:8000/analytics/api/popular-recipes/')
if response.status_code == 200:
    data = response.json()
    for recipe in data['recipes']:
        print(f"{recipe['title']}: {recipe['popularity_score']} points")
```

## Rate Limiting
Currently, no rate limiting is implemented. For production use, consider implementing rate limiting to prevent abuse.

## Caching
- Recipe statistics are cached for 5 minutes
- Popular recipes are cached for 15 minutes
- Search suggestions are cached for 1 hour

## Data Freshness
- Recipe statistics update in real-time when users interact with recipes
- Popular recipes ranking updates every hour
- Search suggestions update based on recent search patterns

## Integration with Frontend

### Django Template Integration
```python
# Django template context for analytics display
def analytics_dashboard_view(request):
    """Analytics dashboard using pure Python/Django"""
    from recipes.mongodb_service import mongodb_service

    # Get comprehensive analytics data
    analytics_data = {
        'total_recipes': Recipe.objects.count(),
        'popular_recipes': mongodb_service.get_popular_recipes(limit=10),
        'recipe_analytics': mongodb_service.get_recipe_analytics(),
        'user_engagement': mongodb_service.get_user_engagement_stats(),
        'dietary_distribution': Recipe.objects.values('dietary_restrictions').annotate(
            count=Count('id')
        ),
        'difficulty_distribution': Recipe.objects.values('difficulty').annotate(
            count=Count('id')
        )
    }

    return render(request, 'analytics/dashboard.html', analytics_data)
```

```html
<!-- Django template for analytics display -->
<!-- analytics/dashboard.html -->
<div class="analytics-dashboard">
    <div class="row">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h4>{{ total_recipes }}</h4>
                    <p>Total Recipes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h4>{{ recipe_analytics.total_views }}</h4>
                    <p>Total Views</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Recipes Section -->
    <div class="row mt-4">
        <div class="col-12">
            <h3>Popular South Indian Recipes</h3>
            {% for recipe in popular_recipes %}
            <div class="card mb-2">
                <div class="card-body">
                    <h5>{{ recipe.title }}</h5>
                    <p>Views: {{ recipe.view_count }} | Rating: {{ recipe.rating_average }}/5</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
```

### Python-based Search Implementation
```python
# Django view for search with suggestions
def recipe_search_view(request):
    """Search recipes using Python/Django with intelligent suggestions"""
    query = request.GET.get('q', '')
    suggestions = []
    recipes = Recipe.objects.none()

    if query:
        # Search across multiple fields using Django ORM
        recipes = Recipe.objects.filter(
            Q(title__icontains=query) |
            Q(ingredients__icontains=query) |
            Q(description__icontains=query) |
            Q(cooking_tips__icontains=query)
        ).distinct()

        # Generate search suggestions using Python
        if len(query) >= 2:
            # Get recipe titles that match
            title_suggestions = Recipe.objects.filter(
                title__icontains=query
            ).values_list('title', flat=True)[:5]

            # Get ingredient suggestions
            ingredient_suggestions = Recipe.objects.filter(
                ingredients__icontains=query
            ).values_list('title', flat=True)[:3]

            suggestions = list(set(list(title_suggestions) + list(ingredient_suggestions)))

    context = {
        'query': query,
        'recipes': recipes,
        'suggestions': suggestions,
        'total_results': recipes.count()
    }
    return render(request, 'recipes/search_results.html', context)
```

```html
<!-- Django template for search results (No JavaScript needed) -->
<div class="search-container">
    <form method="GET" action="{% url 'recipe_search' %}">
        <div class="input-group mb-3">
            <input type="text" name="q" value="{{ query }}"
                   class="form-control" placeholder="Search South Indian recipes...">
            <button class="btn btn-primary" type="submit">Search</button>
        </div>
    </form>

    {% if suggestions and query %}
    <div class="suggestions mb-3">
        <small class="text-muted">Did you mean:</small>
        {% for suggestion in suggestions %}
        <a href="?q={{ suggestion }}" class="badge bg-light text-dark me-1">{{ suggestion }}</a>
        {% endfor %}
    </div>
    {% endif %}

    <div class="search-results">
        <p>Found {{ total_results }} recipe{{ total_results|pluralize }} for "{{ query }}"</p>
        {% for recipe in recipes %}
        <div class="card mb-3">
            <div class="card-body">
                <h5>{{ recipe.title }}</h5>
                <p>{{ recipe.description|truncatewords:20 }}</p>
                <small class="text-muted">
                    {{ recipe.cooking_time }} min | {{ recipe.get_difficulty_display }}
                </small>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
```

## Security Considerations

1. **Authentication**: Ensure proper user authentication for sensitive endpoints
2. **Authorization**: Verify user permissions before returning data
3. **Input Validation**: Validate all input parameters
4. **Rate Limiting**: Implement rate limiting for production use
5. **CORS**: Configure CORS settings appropriately

## Future Enhancements

1. **Pagination**: Add pagination for large result sets
2. **Filtering**: Add more filtering options for popular recipes
3. **Real-time WebSocket**: Implement WebSocket for real-time updates
4. **Batch Operations**: Add endpoints for batch analytics operations
5. **Export**: Add data export capabilities (CSV, JSON)

---

This API provides a foundation for building rich analytics features and can be extended based on specific requirements.
