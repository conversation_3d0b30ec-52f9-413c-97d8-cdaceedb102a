"""
Analytics URLs for Recipe Recommender Platform

URL patterns for analytics views using Python-only approach.
All analytics functionality is handled server-side with Django views.
"""

from django.urls import path
from . import views

app_name = 'analytics'

urlpatterns = [
    # User analytics dashboard
    path('dashboard/', views.user_analytics_view, name='user_dashboard'),
    
    # Admin analytics dashboard
    path('admin/', views.admin_analytics_view, name='admin_dashboard'),
    
    # Individual recipe analytics
    path('recipe/<int:recipe_id>/', views.recipe_analytics_view, name='recipe_analytics'),
    
    # API endpoints (for future use)
    path('api/user-stats/', views.api_user_stats, name='api_user_stats'),
]
