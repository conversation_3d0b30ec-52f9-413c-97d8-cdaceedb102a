from django import forms
from django.contrib.auth.models import User
from .models import Recipe, UserProfile, CookingActivity

class RecipeForm(forms.ModelForm):
    class Meta:
        model = Recipe
        fields = ['title', 'ingredients', 'instructions', 'cooking_time', 'dietary_restrictions', 'difficulty', 'servings']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Recipe name'}),
            'ingredients': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'List ingredients'}),
            'instructions': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Cooking steps'}),
            'cooking_time': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Minutes'}),
            'dietary_restrictions': forms.Select(attrs={'class': 'form-select'}),
            'difficulty': forms.Select(attrs={'class': 'form-select'}),
            'servings': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Number of people'}),
        }


class UserProfileForm(forms.ModelForm):
    """
    Form for editing user profile information.

    Allows users to update their cooking preferences, skill level,
    dietary restrictions, and personal information.
    """
    class Meta:
        model = UserProfile
        fields = ['bio', 'cooking_skill', 'preferred_dietary', 'location']
        widgets = {
            'bio': forms.Textarea(attrs={
                'rows': 4,
                'placeholder': 'Tell us about your cooking interests and favorite South Indian dishes...',
                'class': 'form-control'
            }),
            'cooking_skill': forms.Select(attrs={'class': 'form-select'}),
            'preferred_dietary': forms.Select(attrs={'class': 'form-select'}),
            'location': forms.TextInput(attrs={
                'placeholder': 'Your location (optional)',
                'class': 'form-control'
            }),
        }
        labels = {
            'bio': 'About Your Cooking Journey',
            'cooking_skill': 'Cooking Skill Level',
            'preferred_dietary': 'Dietary Preference',
            'location': 'Location',
        }
        help_texts = {
            'bio': 'Share your cooking experience and favorite South Indian recipes',
            'cooking_skill': 'Select your current cooking skill level',
            'preferred_dietary': 'Your primary dietary preference for recipe recommendations',
            'location': 'Optional: Help us suggest regional recipes',
        }


class UserUpdateForm(forms.ModelForm):
    """
    Form for updating basic user information.

    Allows users to update their username, email, and basic account details.
    """
    email = forms.EmailField(required=True)

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'placeholder': 'First Name',
                'class': 'form-control'
            }),
            'last_name': forms.TextInput(attrs={
                'placeholder': 'Last Name',
                'class': 'form-control'
            }),
            'email': forms.EmailInput(attrs={
                'placeholder': 'Email Address',
                'class': 'form-control'
            }),
        }

    def clean_email(self):
        """Validate email uniqueness"""
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("This email address is already in use.")
        return email


class CookingActivityForm(forms.ModelForm):
    """
    Form for recording cooking activities.

    Allows users to log when they've tried a recipe and add notes
    about their cooking experience.
    """
    class Meta:
        model = CookingActivity
        fields = ['activity_type', 'notes']
        widgets = {
            'activity_type': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={
                'rows': 3,
                'placeholder': 'How did the recipe turn out? Any modifications you made?',
                'class': 'form-control'
            }),
        }
        labels = {
            'activity_type': 'What did you do?',
            'notes': 'Your Experience',
        }
        help_texts = {
            'notes': 'Share your cooking experience, modifications, or tips for other users',
        }