from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count, Avg, Q
from .models import UserActivity, RecipeAnalytics, SearchAnalytics, DailyStats
from recipes.models import Recipe, CookingActivity


class DifficultyFilter(admin.SimpleListFilter):
    """Custom filter for recipe difficulty levels."""
    title = 'Recipe Difficulty'
    parameter_name = 'difficulty'

    def lookups(self, request, model_admin):
        """Return difficulty level options with actual data from database."""
        from recipes.models import Recipe

        # Get actual difficulty choices from Recipe model
        difficulty_choices = Recipe.DIFFICULTY_CHOICES

        # Add custom time-based filters
        custom_choices = [
            ('quick', 'Quick Recipes (≤20 min)'),
            ('complex', 'Complex Recipes (≥45 min)'),
        ]

        return difficulty_choices + custom_choices

    def queryset(self, request, queryset):
        """Filter queryset based on difficulty selection."""
        if self.value() == 'easy':
            return queryset.filter(recipe__difficulty='easy')
        elif self.value() == 'medium':
            return queryset.filter(recipe__difficulty='medium')
        elif self.value() == 'hard':
            return queryset.filter(recipe__difficulty='hard')
        elif self.value() == 'quick':
            return queryset.filter(recipe__cooking_time__lte=20)
        elif self.value() == 'complex':
            return queryset.filter(recipe__cooking_time__gte=45)
        return queryset


class CategoryFilter(admin.SimpleListFilter):
    """Custom filter for recipe categories based on recipe titles."""
    title = 'Recipe Category'
    parameter_name = 'category'

    def lookups(self, request, model_admin):
        """Return category options based on South Indian cuisine with recipe counts."""
        from recipes.models import Recipe
        from django.db.models import Q

        # Define categories with their keywords
        categories = [
            ('breakfast', 'Breakfast Items'),
            ('main_course', 'Main Course'),
            ('snacks', 'Snacks & Appetizers'),
            ('desserts', 'Sweets & Desserts'),
            ('beverages', 'Traditional Beverages'),
            ('rice_dishes', 'Rice Dishes'),
            ('curry_dishes', 'Curry & Gravy'),
            ('festival', 'Festival Specials'),
        ]

        # Add recipe counts to category names
        category_with_counts = []
        for category_key, category_name in categories:
            count = self._get_category_count(category_key)
            if count > 0:
                category_with_counts.append((category_key, f'{category_name} ({count})'))
            else:
                category_with_counts.append((category_key, category_name))

        return category_with_counts

    def _get_category_count(self, category_key):
        """Get count of recipes in a specific category."""
        from recipes.models import Recipe
        from django.db.models import Q

        category_keywords = {
            'breakfast': ['dosa', 'idli', 'uttapam', 'upma', 'poha', 'rava'],
            'main_course': ['sambar', 'rasam', 'bisi bele', 'puliyodarai', 'curd rice'],
            'snacks': ['vada', 'bonda', 'bajji', 'murukku', 'mixture', 'pakoda'],
            'desserts': ['payasam', 'mysore pak', 'holige', 'kesari', 'laddu', 'halwa'],
            'beverages': ['filter coffee', 'masala chai', 'buttermilk', 'panaka'],
            'rice_dishes': ['rice', 'biryani', 'pulao', 'pongal', 'chitranna'],
            'curry_dishes': ['curry', 'kootu', 'poriyal', 'thoran', 'palya'],
            'festival': ['festival', 'special', 'traditional', 'celebration']
        }

        if category_key in category_keywords:
            keywords = category_keywords[category_key]
            q_objects = Q()
            for keyword in keywords:
                q_objects |= Q(title__icontains=keyword)
            return Recipe.objects.filter(q_objects).count()
        return 0

    def queryset(self, request, queryset):
        """Filter queryset based on category selection."""
        category_keywords = {
            'breakfast': ['dosa', 'idli', 'uttapam', 'upma', 'poha', 'rava'],
            'main_course': ['sambar', 'rasam', 'bisi bele', 'puliyodarai', 'curd rice'],
            'snacks': ['vada', 'bonda', 'bajji', 'murukku', 'mixture', 'pakoda'],
            'desserts': ['payasam', 'mysore pak', 'holige', 'kesari', 'laddu', 'halwa'],
            'beverages': ['filter coffee', 'masala chai', 'buttermilk', 'panaka'],
            'rice_dishes': ['rice', 'biryani', 'pulao', 'pongal', 'chitranna'],
            'curry_dishes': ['curry', 'kootu', 'poriyal', 'thoran', 'palya'],
            'festival': ['festival', 'special', 'traditional', 'celebration']
        }

        if self.value() in category_keywords:
            keywords = category_keywords[self.value()]
            q_objects = Q()
            for keyword in keywords:
                q_objects |= Q(recipe__title__icontains=keyword)
            return queryset.filter(q_objects)
        return queryset


class DietaryFilter(admin.SimpleListFilter):
    """Custom filter for dietary restrictions."""
    title = 'Dietary Restrictions'
    parameter_name = 'dietary'

    def lookups(self, request, model_admin):
        """Return dietary restriction options with recipe counts."""
        from recipes.models import Recipe

        # Get actual dietary choices from Recipe model
        dietary_choices = Recipe.DIETARY_CHOICES

        # Add recipe counts to dietary options
        dietary_with_counts = []
        for choice_key, choice_name in dietary_choices:
            count = Recipe.objects.filter(dietary_restrictions=choice_key).count()
            if count > 0:
                dietary_with_counts.append((choice_key, f'{choice_name} ({count})'))
            else:
                dietary_with_counts.append((choice_key, choice_name))

        return dietary_with_counts

    def queryset(self, request, queryset):
        """Filter queryset based on dietary selection."""
        if self.value():
            return queryset.filter(recipe__dietary_restrictions=self.value())
        return queryset


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    """Enhanced admin interface for User Activity with category and difficulty filters."""

    list_display = (
        'user', 'activity_type', 'get_recipe_title', 'get_recipe_difficulty',
        'get_recipe_category', 'get_dietary_info', 'timestamp'
    )
    list_filter = (
        'activity_type', DifficultyFilter, CategoryFilter, DietaryFilter, 'timestamp'
    )
    search_fields = ('user__username', 'recipe__title', 'notes')
    date_hierarchy = 'timestamp'
    ordering = ('-timestamp',)

    fieldsets = (
        ('Activity Information', {
            'fields': ('user', 'activity_type', 'timestamp')
        }),
        ('Recipe Details', {
            'fields': ('recipe',),
            'description': 'Recipe associated with this activity'
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )

    def get_recipe_title(self, obj):
        """Display recipe title with link."""
        if obj.recipe:
            return format_html(
                '<a href="/admin/recipes/recipe/{}/change/">{}</a>',
                obj.recipe.id, obj.recipe.title
            )
        return '-'
    get_recipe_title.short_description = 'Recipe'
    get_recipe_title.admin_order_field = 'recipe__title'

    def get_recipe_difficulty(self, obj):
        """Display recipe difficulty with color coding."""
        if obj.recipe:
            colors = {
                'easy': '#28a745',    # Green
                'medium': '#ffc107',  # Yellow
                'hard': '#dc3545'     # Red
            }
            color = colors.get(obj.recipe.difficulty, '#6c757d')
            return format_html(
                '<span style="color: {}; font-weight: bold;">{}</span>',
                color, obj.recipe.get_difficulty_display()
            )
        return '-'
    get_recipe_difficulty.short_description = 'Difficulty'
    get_recipe_difficulty.admin_order_field = 'recipe__difficulty'

    def get_recipe_category(self, obj):
        """Display inferred recipe category."""
        if obj.recipe:
            title_lower = obj.recipe.title.lower()
            if any(word in title_lower for word in ['dosa', 'idli', 'uttapam', 'upma']):
                return format_html('<span style="color: #17a2b8;">🥞 Breakfast</span>')
            elif any(word in title_lower for word in ['sambar', 'rasam', 'rice']):
                return format_html('<span style="color: #28a745;">🍛 Main Course</span>')
            elif any(word in title_lower for word in ['vada', 'bonda', 'bajji']):
                return format_html('<span style="color: #fd7e14;">🍿 Snacks</span>')
            elif any(word in title_lower for word in ['payasam', 'mysore pak', 'kesari']):
                return format_html('<span style="color: #e83e8c;">🍰 Desserts</span>')
            else:
                return format_html('<span style="color: #6c757d;">🍽️ Other</span>')
        return '-'
    get_recipe_category.short_description = 'Category'

    def get_dietary_info(self, obj):
        """Display dietary restrictions with icons."""
        if obj.recipe:
            dietary_icons = {
                'vegetarian': '🥛 Vegetarian',
                'vegan': '🌱 Vegan',
                'gluten_free': '🌾 Gluten Free',
                'dairy_free': '🥥 Dairy Free',
                'none': '🍽️ No Restrictions'
            }
            return dietary_icons.get(obj.recipe.dietary_restrictions, '🍽️ Unknown')
        return '-'
    get_dietary_info.short_description = 'Dietary'


@admin.register(RecipeAnalytics)
class RecipeAnalyticsAdmin(admin.ModelAdmin):
    """Enhanced admin interface for Recipe Analytics with comprehensive filtering."""

    list_display = (
        'get_recipe_title', 'get_recipe_difficulty', 'get_recipe_category',
        'total_views', 'total_likes', 'get_popularity_score', 'get_engagement_rate'
    )
    list_filter = (DifficultyFilter, CategoryFilter, DietaryFilter, 'last_updated')
    search_fields = ('recipe__title', 'recipe__ingredients', 'recipe__description')
    ordering = ('-total_views', '-total_likes')

    fieldsets = (
        ('Recipe Information', {
            'fields': ('recipe',)
        }),
        ('Analytics Data', {
            'fields': ('total_views', 'total_likes', 'last_updated'),
            'description': 'Recipe performance metrics'
        }),
    )

    def get_recipe_title(self, obj):
        """Display recipe title with admin link."""
        return format_html(
            '<a href="/admin/recipes/recipe/{}/change/">{}</a>',
            obj.recipe.id, obj.recipe.title
        )
    get_recipe_title.short_description = 'Recipe'
    get_recipe_title.admin_order_field = 'recipe__title'

    def get_recipe_difficulty(self, obj):
        """Display difficulty with visual indicators."""
        difficulty_display = {
            'easy': '🟢 Easy',
            'medium': '🟡 Medium',
            'hard': '🔴 Hard'
        }
        return difficulty_display.get(obj.recipe.difficulty, '⚪ Unknown')
    get_recipe_difficulty.short_description = 'Difficulty'
    get_recipe_difficulty.admin_order_field = 'recipe__difficulty'

    def get_recipe_category(self, obj):
        """Display recipe category with emojis."""
        title_lower = obj.recipe.title.lower()
        if any(word in title_lower for word in ['dosa', 'idli', 'uttapam', 'upma']):
            return '🥞 Breakfast'
        elif any(word in title_lower for word in ['sambar', 'rasam', 'rice']):
            return '🍛 Main Course'
        elif any(word in title_lower for word in ['vada', 'bonda', 'bajji']):
            return '🍿 Snacks'
        elif any(word in title_lower for word in ['payasam', 'mysore pak', 'kesari']):
            return '🍰 Desserts'
        return '🍽️ Other'
    get_recipe_category.short_description = 'Category'

    def get_popularity_score(self, obj):
        """Calculate and display popularity score."""
        score = (obj.total_views * 1) + (obj.total_likes * 5)
        if score >= 100:
            return format_html('<span style="color: #28a745; font-weight: bold;">🔥 {}</span>', score)
        elif score >= 50:
            return format_html('<span style="color: #ffc107; font-weight: bold;">⭐ {}</span>', score)
        else:
            return format_html('<span style="color: #6c757d;">📊 {}</span>', score)
    get_popularity_score.short_description = 'Popularity'

    def get_engagement_rate(self, obj):
        """Calculate engagement rate (likes/views ratio)."""
        if obj.total_views > 0:
            rate = (obj.total_likes / obj.total_views) * 100
            if rate >= 20:
                return format_html('<span style="color: #28a745;">💚 {:.1f}%</span>', rate)
            elif rate >= 10:
                return format_html('<span style="color: #ffc107;">💛 {:.1f}%</span>', rate)
            else:
                return format_html('<span style="color: #dc3545;">❤️ {:.1f}%</span>', rate)
        return '0%'
    get_engagement_rate.short_description = 'Engagement'


@admin.register(SearchAnalytics)
class SearchAnalyticsAdmin(admin.ModelAdmin):
    """Enhanced admin interface for Search Analytics with category insights."""

    list_display = (
        'query', 'user', 'get_query_category', 'get_query_difficulty',
        'get_results_count', 'timestamp'
    )
    list_filter = ('timestamp', 'user')
    search_fields = ('query', 'user__username')
    date_hierarchy = 'timestamp'
    ordering = ('-timestamp',)

    def get_query_category(self, obj):
        """Analyze search query to determine likely category interest."""
        query_lower = obj.query.lower()
        if any(word in query_lower for word in ['dosa', 'idli', 'uttapam', 'upma', 'breakfast']):
            return format_html('<span style="color: #17a2b8;">🥞 Breakfast</span>')
        elif any(word in query_lower for word in ['sambar', 'rasam', 'rice', 'main', 'lunch', 'dinner']):
            return format_html('<span style="color: #28a745;">🍛 Main Course</span>')
        elif any(word in query_lower for word in ['vada', 'bonda', 'bajji', 'snack', 'tea']):
            return format_html('<span style="color: #fd7e14;">🍿 Snacks</span>')
        elif any(word in query_lower for word in ['payasam', 'mysore pak', 'sweet', 'dessert']):
            return format_html('<span style="color: #e83e8c;">🍰 Desserts</span>')
        elif any(word in query_lower for word in ['easy', 'quick', 'simple']):
            return format_html('<span style="color: #6c757d;">⚡ Easy Recipes</span>')
        elif any(word in query_lower for word in ['traditional', 'authentic', 'festival']):
            return format_html('<span style="color: #6f42c1;">🎭 Traditional</span>')
        return format_html('<span style="color: #6c757d;">🔍 General</span>')
    get_query_category.short_description = 'Category Interest'

    def get_query_difficulty(self, obj):
        """Analyze search query to determine difficulty preference."""
        query_lower = obj.query.lower()
        if any(word in query_lower for word in ['easy', 'simple', 'quick', 'beginner']):
            return format_html('<span style="color: #28a745;">🟢 Easy</span>')
        elif any(word in query_lower for word in ['medium', 'intermediate', 'traditional']):
            return format_html('<span style="color: #ffc107;">🟡 Medium</span>')
        elif any(word in query_lower for word in ['hard', 'complex', 'advanced', 'festival']):
            return format_html('<span style="color: #dc3545;">🔴 Hard</span>')
        return format_html('<span style="color: #6c757d;">⚪ Any</span>')
    get_query_difficulty.short_description = 'Difficulty Interest'

    def get_results_count(self, obj):
        """Show estimated results count for the search query."""
        from recipes.models import Recipe
        from django.db.models import Q

        # Simulate search results count
        recipes = Recipe.objects.filter(
            Q(title__icontains=obj.query) |
            Q(ingredients__icontains=obj.query) |
            Q(instructions__icontains=obj.query) |
            Q(description__icontains=obj.query)
        )
        count = recipes.count()

        if count >= 10:
            return format_html('<span style="color: #28a745; font-weight: bold;">📊 {} results</span>', count)
        elif count >= 5:
            return format_html('<span style="color: #ffc107; font-weight: bold;">📊 {} results</span>', count)
        elif count > 0:
            return format_html('<span style="color: #dc3545; font-weight: bold;">📊 {} results</span>', count)
        else:
            return format_html('<span style="color: #6c757d;">📊 No results</span>')
    get_results_count.short_description = 'Results Found'


@admin.register(DailyStats)
class DailyStatsAdmin(admin.ModelAdmin):
    """Enhanced admin interface for Daily Statistics with category breakdowns."""

    list_display = (
        'date', 'active_users', 'total_recipe_views', 'get_popular_category',
        'get_popular_difficulty', 'get_engagement_trend'
    )
    list_filter = ('date',)
    date_hierarchy = 'date'
    ordering = ('-date',)

    fieldsets = (
        ('Date Information', {
            'fields': ('date',)
        }),
        ('User Metrics', {
            'fields': ('active_users',),
            'description': 'Daily active user count'
        }),
        ('Content Metrics', {
            'fields': ('total_recipe_views',),
            'description': 'Total recipe views for the day'
        }),
    )

    def get_popular_category(self, obj):
        """Determine most popular recipe category for the day."""
        from recipes.models import Recipe
        from django.db.models import Count

        # Get recipes viewed on this date (simplified logic)
        # In a real implementation, you'd track this in UserActivity
        breakfast_count = Recipe.objects.filter(
            title__iregex=r'\b(dosa|idli|uttapam|upma)\b'
        ).count()
        main_course_count = Recipe.objects.filter(
            title__iregex=r'\b(sambar|rasam|rice)\b'
        ).count()
        snacks_count = Recipe.objects.filter(
            title__iregex=r'\b(vada|bonda|bajji)\b'
        ).count()
        desserts_count = Recipe.objects.filter(
            title__iregex=r'\b(payasam|mysore pak|kesari)\b'
        ).count()

        categories = {
            'Breakfast': breakfast_count,
            'Main Course': main_course_count,
            'Snacks': snacks_count,
            'Desserts': desserts_count
        }

        popular_category = max(categories, key=categories.get)
        icons = {
            'Breakfast': '🥞',
            'Main Course': '🍛',
            'Snacks': '🍿',
            'Desserts': '🍰'
        }

        return format_html(
            '<span style="color: #28a745; font-weight: bold;">{} {}</span>',
            icons.get(popular_category, '🍽️'), popular_category
        )
    get_popular_category.short_description = 'Popular Category'

    def get_popular_difficulty(self, obj):
        """Determine most popular difficulty level for the day."""
        from recipes.models import Recipe

        difficulty_counts = {
            'easy': Recipe.objects.filter(difficulty='easy').count(),
            'medium': Recipe.objects.filter(difficulty='medium').count(),
            'hard': Recipe.objects.filter(difficulty='hard').count()
        }

        popular_difficulty = max(difficulty_counts, key=difficulty_counts.get)
        difficulty_display = {
            'easy': '🟢 Easy',
            'medium': '🟡 Medium',
            'hard': '🔴 Hard'
        }

        return format_html(
            '<span style="font-weight: bold;">{}</span>',
            difficulty_display.get(popular_difficulty, '⚪ Unknown')
        )
    get_popular_difficulty.short_description = 'Popular Difficulty'

    def get_engagement_trend(self, obj):
        """Show engagement trend indicator."""
        # Simplified engagement calculation
        if obj.total_recipe_views > 0 and obj.active_users > 0:
            views_per_user = obj.total_recipe_views / obj.active_users
            if views_per_user >= 5:
                return format_html('<span style="color: #28a745;">📈 High Engagement</span>')
            elif views_per_user >= 3:
                return format_html('<span style="color: #ffc107;">📊 Medium Engagement</span>')
            else:
                return format_html('<span style="color: #dc3545;">📉 Low Engagement</span>')
        return format_html('<span style="color: #6c757d;">📊 No Data</span>')
    get_engagement_trend.short_description = 'Engagement Trend'


# Custom admin site configuration
admin.site.site_header = "Recipe Recommender Analytics"
admin.site.site_title = "Recipe Analytics Admin"
admin.site.index_title = "Analytics Dashboard"

# Add custom CSS for better visual presentation
class AdminConfig:
    """Custom admin configuration for enhanced visual presentation."""

    class Media:
        css = {
            'all': ('admin/css/custom_admin.css',)
        }
        js = ('admin/js/custom_admin.js',)
