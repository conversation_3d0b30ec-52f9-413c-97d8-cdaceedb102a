#!/usr/bin/env python
"""
Simple MongoDB Test for Recipe Recommender Platform
Windows-compatible version without Unicode characters
"""

import sys
import os

try:
    from pymongo import MongoClient
    PYMONGO_AVAILABLE = True
    print("SUCCESS: PyMongo is available")
except ImportError:
    PYMONGO_AVAILABLE = False
    print("ERROR: PyMongo is not available")
    sys.exit(1)

def test_mongodb_connection():
    """Test MongoDB connection - Windows compatible"""
    print("\nTesting MongoDB Connection...")
    
    try:
        # Connect to MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        
        # Test the connection
        client.admin.command('ping')
        print("SUCCESS: MongoDB connection successful!")
        
        # Access the recipe database
        db = client['recipe_recommender_db']
        print("SUCCESS: Connected to database: recipe_recommender_db")
        
        # Test basic operations
        test_collection = db['test_recipes']
        
        # Insert a test recipe
        test_recipe = {
            'title': 'Test South Indian Recipe',
            'ingredients': 'Rice, Lentils, Spices',
            'cooking_time': 30,
            'difficulty': 'easy',
            'dietary_restrictions': 'vegetarian',
            'view_count': 0,
            'test_entry': True
        }
        
        result = test_collection.insert_one(test_recipe)
        print(f"SUCCESS: Test recipe inserted with ID: {result.inserted_id}")
        
        # Query the test recipe
        found_recipe = test_collection.find_one({'test_entry': True})
        if found_recipe:
            print(f"SUCCESS: Test recipe found: {found_recipe['title']}")
        
        # Update the test recipe
        test_collection.update_one(
            {'_id': result.inserted_id},
            {'$inc': {'view_count': 1}}
        )
        print("SUCCESS: Test recipe updated successfully")
        
        # Clean up - remove test recipe
        test_collection.delete_one({'_id': result.inserted_id})
        print("SUCCESS: Test recipe cleaned up")
        
        # List existing collections
        collections = db.list_collection_names()
        print(f"INFO: Existing collections: {len(collections)} found")
        
        # Test analytics operations
        if 'recipes_recipe' in collections:
            recipe_count = db['recipes_recipe'].count_documents({})
            print(f"INFO: Total recipes in database: {recipe_count}")
        
        client.close()
        print("SUCCESS: MongoDB connection closed successfully")
        return True
        
    except Exception as e:
        print(f"ERROR: MongoDB test failed: {e}")
        return False

def create_sample_recipe():
    """Create a sample South Indian recipe in MongoDB"""
    print("\nCreating sample South Indian recipe...")
    
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['recipe_recommender_db']
        
        # Sample recipe data
        sample_recipe = {
            'django_id': 1,
            'title': 'Authentic Sambar',
            'description': 'Traditional South Indian lentil curry with vegetables',
            'ingredients': 'Toor dal, Tamarind, Drumsticks, Okra, Tomatoes, Onions, Sambar powder, Turmeric, Salt, Curry leaves, Mustard seeds, Cumin seeds, Asafoetida, Oil',
            'instructions': 'Cook toor dal until soft. Extract tamarind juice. Cook vegetables separately. Prepare tempering with mustard seeds, cumin, curry leaves. Combine all ingredients and simmer. Add sambar powder and salt. Garnish with coriander leaves.',
            'cooking_time': 45,
            'dietary_restrictions': 'vegetarian',
            'difficulty': 'medium',
            'servings': 4,
            'view_count': 0,
            'rating_average': 0.0,
            'rating_count': 0,
            'created_at': '2024-01-01T10:00:00Z'
        }
        
        # Insert or update the recipe
        collection = db['recipes_recipe']
        result = collection.update_one(
            {'django_id': 1},
            {'$set': sample_recipe},
            upsert=True
        )
        
        if result.upserted_id:
            print("SUCCESS: New sample recipe created")
        else:
            print("SUCCESS: Sample recipe updated")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"ERROR: Sample recipe creation failed: {e}")
        return False

def get_analytics():
    """Get basic analytics from MongoDB"""
    print("\nGetting analytics data...")
    
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['recipe_recommender_db']
        
        # Recipe statistics
        recipes_collection = db['recipes_recipe']
        total_recipes = recipes_collection.count_documents({})
        print(f"INFO: Total recipes: {total_recipes}")
        
        if total_recipes > 0:
            # Get popular recipes
            popular_recipes = list(
                recipes_collection.find()
                .sort('view_count', -1)
                .limit(5)
            )
            
            print("INFO: Popular recipes:")
            for i, recipe in enumerate(popular_recipes, 1):
                title = recipe.get('title', 'Unknown')
                views = recipe.get('view_count', 0)
                print(f"  {i}. {title} ({views} views)")
        
        # User activities
        activities_collection = db['user_activities']
        total_activities = activities_collection.count_documents({})
        print(f"INFO: Total user activities: {total_activities}")
        
        # Search analytics
        search_collection = db['search_analytics']
        total_searches = search_collection.count_documents({})
        print(f"INFO: Total searches: {total_searches}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"ERROR: Analytics failed: {e}")
        return False

if __name__ == '__main__':
    print("Recipe Recommender Platform - MongoDB Test")
    print("=" * 50)
    
    # Test MongoDB connection
    if test_mongodb_connection():
        print("\nSUCCESS: MongoDB connection test passed!")
        
        # Create sample recipe
        if create_sample_recipe():
            print("SUCCESS: Sample recipe creation passed!")
        
        # Get analytics
        if get_analytics():
            print("SUCCESS: Analytics test passed!")
        
        print("\nSUCCESS: All MongoDB tests completed!")
        print("\nINFO: Your Recipe Recommender Platform is ready!")
        print("- MongoDB connection: Working")
        print("- Basic operations: Working") 
        print("- Analytics queries: Working")
        print("- Sample data: Available")
        
    else:
        print("\nERROR: MongoDB connection test failed!")
        print("Please ensure MongoDB is running on localhost:27017")
