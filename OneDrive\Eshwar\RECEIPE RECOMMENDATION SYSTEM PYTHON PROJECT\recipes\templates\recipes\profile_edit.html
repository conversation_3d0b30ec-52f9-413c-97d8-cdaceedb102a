{% extends 'simple_base.html' %}

{% block title %}{{ page_title }} - Recipe Recommender{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- <PERSON> Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Edit Profile</h2>
                <a href="{% url 'profile_view' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Profile
                </a>
            </div>

            <!-- Success Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Profile Edit Form -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Personal Information Section -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">Personal Information</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ user_form.first_name.id_for_label }}" class="form-label">
                                        First Name
                                    </label>
                                    {{ user_form.first_name }}
                                    {% if user_form.first_name.errors %}
                                        <div class="text-danger small">
                                            {{ user_form.first_name.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ user_form.last_name.id_for_label }}" class="form-label">
                                        Last Name
                                    </label>
                                    {{ user_form.last_name }}
                                    {% if user_form.last_name.errors %}
                                        <div class="text-danger small">
                                            {{ user_form.last_name.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="{{ user_form.email.id_for_label }}" class="form-label">
                                    Email Address
                                </label>
                                {{ user_form.email }}
                                {% if user_form.email.errors %}
                                    <div class="text-danger small">
                                        {{ user_form.email.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Cooking Profile Section -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">Cooking Profile</h5>
                            
                            <!-- Bio -->
                            <div class="mb-3">
                                <label for="{{ profile_form.bio.id_for_label }}" class="form-label">
                                    {{ profile_form.bio.label }}
                                </label>
                                {{ profile_form.bio }}
                                <div class="form-text">{{ profile_form.bio.help_text }}</div>
                                {% if profile_form.bio.errors %}
                                    <div class="text-danger small">
                                        {{ profile_form.bio.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Cooking Skill and Dietary Preference -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ profile_form.cooking_skill.id_for_label }}" class="form-label">
                                        {{ profile_form.cooking_skill.label }}
                                    </label>
                                    {{ profile_form.cooking_skill }}
                                    <div class="form-text">{{ profile_form.cooking_skill.help_text }}</div>
                                    {% if profile_form.cooking_skill.errors %}
                                        <div class="text-danger small">
                                            {{ profile_form.cooking_skill.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ profile_form.preferred_dietary.id_for_label }}" class="form-label">
                                        {{ profile_form.preferred_dietary.label }}
                                    </label>
                                    {{ profile_form.preferred_dietary }}
                                    <div class="form-text">{{ profile_form.preferred_dietary.help_text }}</div>
                                    {% if profile_form.preferred_dietary.errors %}
                                        <div class="text-danger small">
                                            {{ profile_form.preferred_dietary.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Location -->
                            <div class="mb-3">
                                <label for="{{ profile_form.location.id_for_label }}" class="form-label">
                                    {{ profile_form.location.label }}
                                </label>
                                {{ profile_form.location }}
                                <div class="form-text">{{ profile_form.location.help_text }}</div>
                                {% if profile_form.location.errors %}
                                    <div class="text-danger small">
                                        {{ profile_form.location.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Cooking Skill Level Guide -->
                        <div class="mb-4">
                            <h6 class="text-muted">Cooking Skill Level Guide:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled small">
                                        <li><span class="badge bg-success me-2">Beginner</span> New to cooking, learning basics</li>
                                        <li><span class="badge bg-info me-2">Intermediate</span> Comfortable with common recipes</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled small">
                                        <li><span class="badge bg-warning me-2">Advanced</span> Experienced with complex dishes</li>
                                        <li><span class="badge bg-danger me-2">Expert</span> Professional or master-level skills</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Dietary Preferences Guide -->
                        <div class="mb-4">
                            <h6 class="text-muted">Dietary Preferences:</h6>
                            <div class="row">
                                <div class="col-md-12">
                                    <p class="small text-muted">
                                        Select your primary dietary preference to receive personalized South Indian recipe recommendations. 
                                        This helps us suggest recipes that match your dietary needs and restrictions.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'profile_view' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Profile Tips -->
            <div class="card mt-4 bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb text-warning"></i> Profile Tips
                    </h6>
                    <ul class="mb-0 small">
                        <li>Complete your profile to get better recipe recommendations</li>
                        <li>Share your cooking interests in the bio to connect with similar food enthusiasts</li>
                        <li>Update your dietary preferences to discover recipes that match your needs</li>
                        <li>Your cooking skill level helps us suggest appropriate recipes for your experience</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS for alerts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}
