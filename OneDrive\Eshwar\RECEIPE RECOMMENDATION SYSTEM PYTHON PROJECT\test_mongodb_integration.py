#!/usr/bin/env python
"""
Test MongoDB Integration for Recipe Recommender Platform

This script tests the MongoDB connection and basic operations
without Django dependencies to ensure the database integration works.
"""

import sys
import os

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pymongo import MongoClient
    PYMONGO_AVAILABLE = True
    print("✅ PyMongo is available")
except ImportError:
    PYMONGO_AVAILABLE = False
    print("❌ PyMongo is not available")
    sys.exit(1)

def test_mongodb_connection():
    """Test MongoDB connection and basic operations"""
    print("\n🔍 Testing MongoDB Connection...")
    
    try:
        # Connect to MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        
        # Test the connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful!")
        
        # Access the recipe database
        db = client['recipe_recommender_db']
        print(f"✅ Connected to database: recipe_recommender_db")
        
        # Test basic operations
        test_collection = db['test_recipes']
        
        # Insert a test recipe
        test_recipe = {
            'title': 'Test South Indian Recipe',
            'ingredients': 'Rice, Lentils, Spices',
            'cooking_time': 30,
            'difficulty': 'easy',
            'dietary_restrictions': 'vegetarian',
            'view_count': 0,
            'test_entry': True
        }
        
        result = test_collection.insert_one(test_recipe)
        print(f"✅ Test recipe inserted with ID: {result.inserted_id}")
        
        # Query the test recipe
        found_recipe = test_collection.find_one({'test_entry': True})
        if found_recipe:
            print(f"✅ Test recipe found: {found_recipe['title']}")
        
        # Update the test recipe
        test_collection.update_one(
            {'_id': result.inserted_id},
            {'$inc': {'view_count': 1}}
        )
        print("✅ Test recipe updated successfully")
        
        # Clean up - remove test recipe
        test_collection.delete_one({'_id': result.inserted_id})
        print("✅ Test recipe cleaned up")
        
        # List existing collections
        collections = db.list_collection_names()
        print(f"📋 Existing collections: {collections}")
        
        # Test analytics operations
        if 'recipes_recipe' in collections:
            recipe_count = db['recipes_recipe'].count_documents({})
            print(f"📊 Total recipes in database: {recipe_count}")
            
            # Get popular recipes
            popular_recipes = list(db['recipes_recipe'].find().sort('view_count', -1).limit(5))
            print(f"🔥 Found {len(popular_recipes)} recipes for popularity analysis")
        
        client.close()
        print("✅ MongoDB connection closed successfully")
        return True
        
    except Exception as e:
        print(f"❌ MongoDB test failed: {e}")
        return False

def test_recipe_analytics():
    """Test recipe analytics operations"""
    print("\n📊 Testing Recipe Analytics...")
    
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['recipe_recommender_db']
        
        # Test aggregation pipeline for analytics
        pipeline = [
            {
                '$group': {
                    '_id': None,
                    'total_recipes': {'$sum': 1},
                    'avg_cooking_time': {'$avg': '$cooking_time'},
                    'total_views': {'$sum': '$view_count'}
                }
            }
        ]
        
        if 'recipes_recipe' in db.list_collection_names():
            result = list(db['recipes_recipe'].aggregate(pipeline))
            if result:
                stats = result[0]
                print(f"✅ Analytics calculated:")
                print(f"   - Total recipes: {stats.get('total_recipes', 0)}")
                print(f"   - Average cooking time: {stats.get('avg_cooking_time', 0):.1f} minutes")
                print(f"   - Total views: {stats.get('total_views', 0)}")
            else:
                print("⚠️ No recipe data found for analytics")
        else:
            print("⚠️ No recipes collection found")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Analytics test failed: {e}")
        return False

def create_sample_analytics_data():
    """Create sample analytics data for testing"""
    print("\n🎯 Creating Sample Analytics Data...")
    
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['recipe_recommender_db']
        
        # Sample user activities
        activities = [
            {
                'user_id': 1,
                'activity_type': 'view',
                'recipe_id': 1,
                'timestamp': '2024-01-01T10:00:00Z',
                'ip_address': '127.0.0.1'
            },
            {
                'user_id': 1,
                'activity_type': 'like',
                'recipe_id': 1,
                'timestamp': '2024-01-01T10:05:00Z',
                'ip_address': '127.0.0.1'
            },
            {
                'user_id': 2,
                'activity_type': 'view',
                'recipe_id': 2,
                'timestamp': '2024-01-01T11:00:00Z',
                'ip_address': '*********'
            }
        ]
        
        # Insert sample activities
        activities_collection = db['user_activities']
        activities_collection.delete_many({'ip_address': {'$in': ['127.0.0.1', '*********']}})  # Clean up first
        result = activities_collection.insert_many(activities)
        print(f"✅ Inserted {len(result.inserted_ids)} sample activities")
        
        # Sample search analytics
        searches = [
            {
                'query': 'sambar',
                'user_id': 1,
                'results_count': 5,
                'timestamp': '2024-01-01T09:00:00Z'
            },
            {
                'query': 'dosa',
                'user_id': 2,
                'results_count': 8,
                'timestamp': '2024-01-01T09:30:00Z'
            }
        ]
        
        search_collection = db['search_analytics']
        search_collection.delete_many({'user_id': {'$in': [1, 2]}})  # Clean up first
        result = search_collection.insert_many(searches)
        print(f"✅ Inserted {len(result.inserted_ids)} sample searches")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        return False

if __name__ == '__main__':
    print("🍛 Recipe Recommender Platform - MongoDB Integration Test")
    print("=" * 60)
    
    # Test MongoDB connection
    if test_mongodb_connection():
        print("\n✅ MongoDB connection test passed!")
    else:
        print("\n❌ MongoDB connection test failed!")
        sys.exit(1)
    
    # Test analytics operations
    if test_recipe_analytics():
        print("\n✅ Analytics test passed!")
    else:
        print("\n❌ Analytics test failed!")
    
    # Create sample data
    if create_sample_analytics_data():
        print("\n✅ Sample data creation passed!")
    else:
        print("\n❌ Sample data creation failed!")
    
    print("\n🎉 All MongoDB tests completed!")
    print("\n💡 Your Recipe Recommender Platform is ready for MongoDB integration!")
    print("   - MongoDB connection: Working ✅")
    print("   - Basic operations: Working ✅") 
    print("   - Analytics queries: Working ✅")
    print("   - Sample data: Created ✅")
    print("\n🚀 You can now run your Django application with MongoDB support!")
