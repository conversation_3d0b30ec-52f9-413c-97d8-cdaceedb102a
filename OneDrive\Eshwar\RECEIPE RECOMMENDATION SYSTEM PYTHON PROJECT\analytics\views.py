"""
Analytics Views for Recipe Recommender Platform

This module contains view functions for displaying analytics and metrics
using a Python-only approach. All data processing and visualization is
handled server-side using Django templates and Python calculations.

Key Features:
- User analytics dashboard
- Recipe performance metrics
- Search analytics
- MongoDB integration for advanced analytics
- Pure Python implementation (no JavaScript required)
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.db.models import Count, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.contrib.auth.models import User

from .models import UserActivity, RecipeAnalytics, SearchAnalytics, DailyStats
from recipes.models import Recipe
from recipes.mongodb_service import mongodb_service


def is_staff(user):
    """Check if user is staff for admin analytics access"""
    return user.is_staff


@login_required
def user_analytics_view(request):
    """
    Personal analytics dashboard for logged-in users.
    
    Shows user's own activity, favorite recipes, and personalized insights
    using Python-based calculations and Django template rendering.
    """
    user = request.user
    
    # Get user's activity data
    user_activities = UserActivity.objects.filter(user=user).order_by('-timestamp')[:20]
    
    # Calculate user statistics
    user_stats = {
        'total_views': UserActivity.objects.filter(user=user, activity_type='view').count(),
        'total_likes': UserActivity.objects.filter(user=user, activity_type='like').count(),
        'total_searches': UserActivity.objects.filter(user=user, activity_type='search').count(),
        'recipes_created': Recipe.objects.filter(author=user).count(),
    }
    
    # Get user's favorite recipes (most viewed by user)
    favorite_recipes = Recipe.objects.filter(
        useractivities__user=user,
        useractivities__activity_type='view'
    ).annotate(
        view_count=Count('useractivities')
    ).order_by('-view_count')[:5]
    
    # Get recent activity
    recent_activities = user_activities[:10]
    
    context = {
        'user_stats': user_stats,
        'favorite_recipes': favorite_recipes,
        'recent_activities': recent_activities,
        'page_title': f'Analytics for {user.username}'
    }
    
    return render(request, 'analytics/user_dashboard.html', context)


@user_passes_test(is_staff)
def admin_analytics_view(request):
    """
    Admin analytics dashboard showing platform-wide metrics.
    
    Displays comprehensive analytics for staff users including recipe
    performance, user engagement, and platform growth metrics.
    """
    # Calculate date ranges
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    # Platform overview statistics
    platform_stats = {
        'total_users': User.objects.count(),
        'total_recipes': Recipe.objects.count(),
        'total_views': UserActivity.objects.filter(activity_type='view').count(),
        'total_searches': UserActivity.objects.filter(activity_type='search').count(),
    }
    
    # Recent activity (last 7 days)
    recent_stats = {
        'new_users': User.objects.filter(date_joined__gte=week_ago).count(),
        'new_recipes': Recipe.objects.filter(created_at__gte=week_ago).count(),
        'recent_views': UserActivity.objects.filter(
            activity_type='view', 
            timestamp__gte=week_ago
        ).count(),
    }
    
    # Popular recipes (using MongoDB if available)
    try:
        popular_recipes = mongodb_service.get_popular_recipes(limit=10)
    except:
        # Fallback to Django ORM if MongoDB unavailable
        popular_recipes = Recipe.objects.annotate(
            view_count=Count('useractivities', filter=Q(useractivities__activity_type='view'))
        ).order_by('-view_count')[:10]
    
    # Recipe analytics
    recipe_analytics = RecipeAnalytics.objects.select_related('recipe').order_by('-engagement_score')[:10]
    
    # Search analytics - most popular search terms
    popular_searches = SearchAnalytics.objects.values('query').annotate(
        search_count=Count('id')
    ).order_by('-search_count')[:10]
    
    # Dietary distribution
    dietary_distribution = Recipe.objects.values('dietary_restrictions').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Calculate percentages for dietary distribution
    total_recipes = Recipe.objects.count()
    for item in dietary_distribution:
        item['percentage'] = (item['count'] / total_recipes * 100) if total_recipes > 0 else 0
    
    context = {
        'platform_stats': platform_stats,
        'recent_stats': recent_stats,
        'popular_recipes': popular_recipes,
        'recipe_analytics': recipe_analytics,
        'popular_searches': popular_searches,
        'dietary_distribution': dietary_distribution,
        'page_title': 'Admin Analytics Dashboard'
    }
    
    return render(request, 'analytics/admin_dashboard.html', context)


def recipe_analytics_view(request, recipe_id):
    """
    Individual recipe analytics view.
    
    Shows detailed analytics for a specific recipe including views,
    engagement metrics, and user feedback.
    """
    recipe = get_object_or_404(Recipe, id=recipe_id)
    
    # Get or create recipe analytics
    analytics, created = RecipeAnalytics.objects.get_or_create(recipe=recipe)
    
    # Update analytics if needed
    if created or analytics.last_updated < timezone.now() - timedelta(hours=1):
        analytics.total_views = UserActivity.objects.filter(
            recipe=recipe, 
            activity_type='view'
        ).count()
        
        analytics.total_likes = UserActivity.objects.filter(
            recipe=recipe, 
            activity_type='like'
        ).count()
        
        analytics.total_favorites = UserActivity.objects.filter(
            recipe=recipe, 
            activity_type='favorite'
        ).count()
        
        analytics.unique_views = UserActivity.objects.filter(
            recipe=recipe, 
            activity_type='view'
        ).values('user').distinct().count()
        
        analytics.calculate_engagement_score()
        analytics.save()
    
    # Get recent activity for this recipe
    recent_activity = UserActivity.objects.filter(recipe=recipe).order_by('-timestamp')[:10]
    
    # Get MongoDB analytics if available
    mongodb_stats = {}
    try:
        mongodb_stats = mongodb_service.get_recipe_analytics()
    except:
        pass
    
    context = {
        'recipe': recipe,
        'analytics': analytics,
        'recent_activity': recent_activity,
        'mongodb_stats': mongodb_stats,
        'page_title': f'Analytics for {recipe.title}'
    }
    
    return render(request, 'analytics/recipe_detail.html', context)


# API Views for AJAX requests (optional, for future use)
@login_required
def api_user_stats(request):
    """API endpoint for user statistics (JSON response)"""
    user = request.user
    
    stats = {
        'total_views': UserActivity.objects.filter(user=user, activity_type='view').count(),
        'total_likes': UserActivity.objects.filter(user=user, activity_type='like').count(),
        'total_searches': UserActivity.objects.filter(user=user, activity_type='search').count(),
        'recipes_created': Recipe.objects.filter(author=user).count(),
    }
    
    return JsonResponse(stats)


def track_activity(request, activity_type, recipe_id=None):
    """
    Track user activity for analytics.
    
    This function can be called from views to record user interactions
    with recipes and the platform.
    """
    if request.user.is_authenticated:
        user = request.user
    else:
        user = None
    
    recipe = None
    if recipe_id:
        try:
            recipe = Recipe.objects.get(id=recipe_id)
        except Recipe.DoesNotExist:
            pass
    
    # Get user's IP address
    ip_address = request.META.get('REMOTE_ADDR')
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    # Create activity record
    UserActivity.objects.create(
        user=user,
        activity_type=activity_type,
        recipe=recipe,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    # Update MongoDB analytics if available
    try:
        if recipe:
            mongodb_service.sync_recipe_to_mongodb(recipe)
    except:
        pass
