#!/usr/bin/env python
"""
Recipe Recommender Platform - Error-Free Launcher

This script provides an error-free way to run the Recipe Recommender Platform
with MongoDB integration, bypassing Django compatibility issues.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_mongodb_connection():
    """Check if MongoDB is running and accessible"""
    print("🔍 Checking MongoDB connection...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_mongodb_connection.py'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ MongoDB connection successful!")
            return True
        else:
            print(f"❌ MongoDB connection failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ MongoDB check failed: {e}")
        return False

def run_mongodb_analytics():
    """Run MongoDB analytics independently"""
    print("\n📊 Running MongoDB Analytics...")
    
    try:
        result = subprocess.run([
            sys.executable, 'simple_mongodb_analytics.py'
        ], timeout=30)
        
        if result.returncode == 0:
            print("✅ MongoDB analytics completed successfully!")
            return True
        else:
            print("❌ MongoDB analytics failed")
            return False
            
    except Exception as e:
        print(f"❌ Analytics execution failed: {e}")
        return False

def try_django_server():
    """Try to run Django development server"""
    print("\n🚀 Attempting to start Django server...")
    
    try:
        # First try to run Django checks
        check_result = subprocess.run([
            sys.executable, 'manage.py', 'check'
        ], capture_output=True, text=True, timeout=30)
        
        if check_result.returncode != 0:
            print(f"⚠️ Django check failed: {check_result.stderr}")
            print("💡 Django has compatibility issues, but MongoDB is working!")
            return False
        
        print("✅ Django checks passed! Starting server...")
        print("🌐 Server will be available at: http://127.0.0.1:8000/")
        print("🛑 Press Ctrl+C to stop the server")
        
        # Run the Django server
        subprocess.run([
            sys.executable, 'manage.py', 'runserver'
        ])
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return True
    except Exception as e:
        print(f"❌ Django server failed: {e}")
        return False

def show_platform_status():
    """Show current platform status and capabilities"""
    print("\n📋 Recipe Recommender Platform Status")
    print("=" * 50)
    
    # Check Python version
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"🐍 Python Version: {python_version}")
    
    # Check Django installation
    try:
        import django
        django_version = django.get_version()
        print(f"🌐 Django Version: {django_version}")
    except Exception as e:
        print(f"❌ Django: Not properly installed ({e})")
    
    # Check PyMongo
    try:
        import pymongo
        pymongo_version = pymongo.version
        print(f"🍃 PyMongo Version: {pymongo_version}")
    except Exception as e:
        print(f"❌ PyMongo: Not available ({e})")
    
    # Check MongoDB connection
    mongodb_status = check_mongodb_connection()
    
    print(f"\n🎯 Platform Capabilities:")
    print(f"   ✅ MongoDB Analytics: {'Working' if mongodb_status else 'Not Available'}")
    print(f"   ✅ Recipe Data Storage: {'Working' if mongodb_status else 'Not Available'}")
    print(f"   ✅ Search Functionality: {'Working' if mongodb_status else 'Not Available'}")
    print(f"   ⚠️ Django Web Interface: Compatibility Issues")
    
    return mongodb_status

def show_usage_instructions():
    """Show instructions for using the platform"""
    print(f"\n📖 How to Use Recipe Recommender Platform:")
    print(f"=" * 50)
    print(f"")
    print(f"🔧 Available Commands:")
    print(f"   python run_recipe_platform.py          - Run this status check")
    print(f"   python test_mongodb_connection.py      - Test MongoDB connection")
    print(f"   python simple_mongodb_analytics.py     - View analytics report")
    print(f"   python test_mongodb_integration.py     - Full MongoDB test")
    print(f"   python fix_django_mongodb.py           - Attempt Django integration")
    print(f"")
    print(f"📊 Analytics Features (Working):")
    print(f"   ✅ Recipe statistics and metrics")
    print(f"   ✅ User activity tracking")
    print(f"   ✅ Search analytics")
    print(f"   ✅ Popular recipe tracking")
    print(f"   ✅ Dietary distribution analysis")
    print(f"")
    print(f"🌐 Web Interface:")
    print(f"   ⚠️ Django web interface has compatibility issues")
    print(f"   💡 All core functionality works through Python scripts")
    print(f"   🔧 Consider using Django 3.1.x for web interface")
    print(f"")
    print(f"🍛 Recipe Data:")
    print(f"   ✅ South Indian recipe collection")
    print(f"   ✅ MongoDB storage and retrieval")
    print(f"   ✅ Advanced search capabilities")
    print(f"   ✅ Analytics and performance tracking")

def main():
    """Main function to run the Recipe Recommender Platform"""
    print("🍛 Recipe Recommender Platform - Error-Free Launcher")
    print("=" * 60)
    
    # Show platform status
    mongodb_working = show_platform_status()
    
    if mongodb_working:
        print(f"\n🎉 Core platform functionality is working!")
        
        # Run analytics
        run_mongodb_analytics()
        
        # Try Django server
        django_working = try_django_server()
        
        if not django_working:
            print(f"\n💡 Django web interface unavailable, but core features work!")
            show_usage_instructions()
    else:
        print(f"\n❌ MongoDB connection failed. Please ensure MongoDB is running.")
        print(f"💡 Start MongoDB and try again.")
    
    print(f"\n✨ Recipe Recommender Platform session completed!")

if __name__ == '__main__':
    main()
