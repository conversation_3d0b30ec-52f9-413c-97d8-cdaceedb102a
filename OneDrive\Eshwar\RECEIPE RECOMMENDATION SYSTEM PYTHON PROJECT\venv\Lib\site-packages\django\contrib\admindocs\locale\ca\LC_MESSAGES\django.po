# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2014,2017,2021
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-10-27 08:46+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Catalan (http://www.transifex.com/django/django/language/"
"ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Documentació Administrativa"

msgid "Home"
msgstr "Inici"

msgid "Documentation"
msgstr "Documentació"

msgid "Bookmarklets"
msgstr "'Bookmarklets'"

msgid "Documentation bookmarklets"
msgstr "'Bookmarklets' de documentació"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Per instal·lar miniaplicacions enllaçades, arrossegueu l'enllaç a la vostra "
"barra de marcados, o feu clic dret a l'enllaç i afegiu-lo als vostres "
"marcadors. Ara podeu seleccionar la miniaplicació des de qualsevol pàgina "
"del lloc."

msgid "Documentation for this page"
msgstr "Documentació d'aquesta pàgina"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Us porta des de qualsevol pàgina a la documentació de la vista que la genera."

msgid "Tags"
msgstr "Etiquetes"

msgid "List of all the template tags and their functions."
msgstr "Llista d'etiquetes de plantilles i les seves funcions."

msgid "Filters"
msgstr "Filtres"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Els filtres són accions que poden ser aplicades a variables a una plantilla "
"per alterar la seva sortida."

msgid "Models"
msgstr "Models"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Els models són descripcions de tots els objectes del sistema i els seus "
"camps associats. Cada model té una llista de camps que són accessibles com "
"variables de la plantilla."

msgid "Views"
msgstr "Vistes"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Cada plana del lloc públic és generat per una vista (view). La vista "
"defineix quina plantilla es fa servir per generar la plana i quins objectes "
"estan disponibles a aquella plantilla."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Eines pel seu navegador per accedir ràpidament a la funcionalitat de l'admin."

msgid "Please install docutils"
msgstr "Si us plau instal·leu docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"El sistema d'administració de documentació requereix de la biblioteca de "
"Python <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Si us plau, demani als seus administradors la instal·lació de <a href="
"\"%(link)s\">docutils</a> ."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Camps"

msgid "Field"
msgstr "Camp"

msgid "Type"
msgstr "Tipus"

msgid "Description"
msgstr "Descripció"

msgid "Methods with arguments"
msgstr "Mètodes amb arguments"

msgid "Method"
msgstr "Mètode"

msgid "Arguments"
msgstr "Arguments"

msgid "Back to Model documentation"
msgstr "Tornar a la documentació del Model"

msgid "Model documentation"
msgstr "Documentació del model"

msgid "Model groups"
msgstr "Grups de model"

msgid "Templates"
msgstr "Plantilles"

#, python-format
msgid "Template: %(name)s"
msgstr "Plantilla: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Plantilla: %(name)s"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Camí de cerca per la plantilla \"%(name)s\":"

msgid "(does not exist)"
msgstr "(no existeix)"

msgid "Back to Documentation"
msgstr "Tornar a la documentació"

msgid "Template filters"
msgstr "Filtres de plantilla"

msgid "Template filter documentation"
msgstr "Documentació dels filtres de plantilla"

msgid "Built-in filters"
msgstr "Filtres integrats"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Per utilitzar aquests filtres, introdueixi <code>%(code)s</code> a la seva "
"plantilla abans d'utilitzar el filtre."

msgid "Template tags"
msgstr "Etiquetes de plantilles"

msgid "Template tag documentation"
msgstr "Documentació de les etiquetes de plantilla"

msgid "Built-in tags"
msgstr "Etiquetes integrades"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Per utilitzar aquestes etiquetes, introdueix <code>%(code)s</code> a la teva "
"plantilla abans d'utilitzar l'etiqueta"

#, python-format
msgid "View: %(name)s"
msgstr "Vista: %(name)s"

msgid "Context:"
msgstr "Contexte:"

msgid "Templates:"
msgstr "Plantilles:"

msgid "Back to View documentation"
msgstr "Tornar a la documentació de les Vistes"

msgid "View documentation"
msgstr "Veure documentació"

msgid "Jump to namespace"
msgstr "Anar a l'espai de noms"

msgid "Empty namespace"
msgstr "Espai de noms buit"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vistes de l'espai de noms %(name)s"

msgid "Views by empty namespace"
msgstr "Vistes per l'espai de noms buit"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Funció de la vista: <code>%(full_name)s</code> . Nom: <code>%(url_name)s</"
"code> .\n"

msgid "tag:"
msgstr "etiqueta:"

msgid "filter:"
msgstr "filtre:"

msgid "view:"
msgstr "vista:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplicació %(app_label)r no trobada"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "El model %(model_name)r no s'ha trobat a l'aplicació %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "l'objecte relacionat `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "objectes relacionats `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "tots %s"

#, python-format
msgid "number of %s"
msgstr "nombre de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s no sembla ser un objecte 'urlpattern'"
