# Recipe Recommender Platform

A comprehensive culinary discovery platform designed to help home cooks, food enthusiasts, and culinary professionals discover and share authentic South Indian recipes. This web application provides an intuitive interface for exploring, creating, and managing traditional recipes with detailed cooking instructions, ingredient lists, and nutritional information.

## Features

- **Authentic South Indian Cuisine**: Curated collection of traditional regional recipes
- **Advanced Recipe Discovery**: Search and filter by dietary restrictions, cooking time, and difficulty
- **Dietary Accommodation**: Support for vegetarian, vegan, gluten-free, and dairy-free options
- **User Engagement**: Rate, review, and save favorite recipes
- **Analytics Dashboard**: Track recipe popularity and user engagement metrics
- **MongoDB Integration**: Dual database architecture for enhanced search and analytics
- **User Authentication**: Secure registration, login, and profile management

## Project Structure

```
recipe_recommender/
├── manage.py
├── requirements.txt
├── recipe_recommender/
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
└── recipes/
    ├── models.py          # Recipe, Category, Review models
    ├── views.py           # Simple view functions
    ├── urls.py            # URL patterns
    ├── forms.py           # Recipe and review forms
    ├── admin.py           # Admin interface
    └── templates/         # HTML templates
        ├── base.html
        ├── recipes/
        │   ├── home.html
        │   ├── recipe_list.html
        │   ├── recipe_detail.html
        │   ├── recipe_form.html
        │   └── add_review.html
        └── registration/
            ├── login.html
            └── register.html
```

## Core Models

### Recipe
- **Basic Information**: Title, description, ingredients, step-by-step instructions
- **Cooking Details**: Preparation time, cooking time, total time, servings, difficulty level
- **Dietary Features**: Dietary restrictions (vegetarian, vegan, gluten-free, dairy-free)
- **Enhancement Features**: Cooking tips, nutritional information, view count tracking
- **User Interaction**: Rating average, rating count, author, creation date

### Category
- **South Indian Focus**: Traditional categories like Rice Dishes, Curries, Snacks, Sweets
- **Regional Specialties**: Tamil, Telugu, Kannada, Malayalam cuisine classifications

### Review & Rating
- **User Feedback**: Recipe rating (1-5 stars), detailed comments and cooking experiences
- **Community Engagement**: User interactions, cooking tips sharing, recipe modifications

### User Profile
- **Dietary Preferences**: Personal dietary restrictions and cooking skill level
- **Cooking History**: Favorite recipes, cooking activity tracking, personalized recommendations

### Analytics (MongoDB)
- **Recipe Performance**: View counts, popularity scores, engagement metrics
- **User Behavior**: Search patterns, cooking preferences, community interactions

## Installation

1. **Clone/Download** the project
2. **Create virtual environment**:
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   ```
3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
4. **Run migrations**:
   ```bash
   python manage.py migrate
   ```
5. **Create superuser** (optional):
   ```bash
   python manage.py createsuperuser
   ```
6. **Run server**:
   ```bash
   python manage.py runserver
   ```

## Usage

1. **Home Page**: Discover featured South Indian recipes and browse by categories
2. **Recipe Discovery**: Advanced search with dietary filters, cooking time, and difficulty levels
3. **Recipe Details**: Comprehensive recipe view with ingredients, instructions, cooking tips, and nutritional info
4. **User Engagement**: Rate, review, and save favorite recipes (requires login)
5. **Personal Dashboard**: Track cooking history and get personalized recommendations
6. **Analytics**: View recipe popularity and community engagement metrics
7. **Admin Panel**: Manage recipes, categories, and users at `/admin/`

## Technology Stack

### Backend (Python)
- **Django 4.2+**: Web framework for robust backend development
- **PyMongo**: Python MongoDB driver for advanced analytics
- **Django REST Framework**: API development for analytics endpoints

### Frontend
- **HTML5/CSS3**: Modern web technologies with Django templates
- **Bootstrap 5+**: Responsive UI framework (CSS-only, no JavaScript dependencies)
- **Python-based Analytics**: Server-side data visualization using Django template system

### Database
- **SQLite**: Primary database for user authentication and core data
- **MongoDB**: Secondary database for analytics and advanced search functionality

## Advanced Features

This comprehensive platform includes:
- **Dual Database Architecture**: SQLite + MongoDB for optimal performance
- **Advanced Analytics**: Recipe popularity tracking and user behavior analysis
- **Personalized Recommendations**: AI-driven recipe suggestions based on user preferences
- **Dietary Accommodation**: Comprehensive filtering for various dietary restrictions
- **Community Engagement**: Rating, review, and favorite systems
- **South Indian Focus**: Curated authentic regional recipe collection

## Development

The project uses Django's built-in features:
- SQLite database
- Django admin interface
- Class-based views
- Template inheritance
- Form handling
- User authentication

## License

Open source project for educational purposes.
