<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe Recommender</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">Recipe Recommender</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto">
                    <a class="nav-link" href="{% url 'home' %}">Home</a>
                    <a class="nav-link" href="{% url 'recipe_list' %}">All Recipes</a>
                    {% if user.is_authenticated %}
                    <a class="nav-link" href="{% url 'recipe_create' %}">Add Recipe</a>
                    {% endif %}
                    <a class="nav-link" href="{% url 'test_page' %}">🧪 Test</a>
                </div>
                <div class="navbar-nav">
                    {% if user.is_authenticated %}
                    <a class="nav-link" href="{% url 'profile_view' %}">
                        <i class="fas fa-user"></i> {{ user.username }}
                    </a>
                    <a class="nav-link" href="{% url 'custom_logout' %}">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                    {% else %}
                    <a class="nav-link" href="{% url 'login' %}">Login</a>
                    <a class="nav-link" href="{% url 'signup' %}">Sign Up</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <main>
        {% block content %}
        {% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
