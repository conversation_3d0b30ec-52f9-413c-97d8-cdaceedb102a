{% extends 'simple_base.html' %}

{% block content %}
<div class="container mt-4">
    <h1>Recipe Recommender</h1>
    <p class="text-muted">🌶️ Authentic South Indian recipes from Tamil Nadu, Karnataka, Kerala, and Andhra Pradesh</p>

    <a href="{% url 'recipe_create' %}" class="btn btn-primary mb-4">Add New Recipe</a>

    <!-- Enhanced Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Search Recipes</label>
                        <input type="text" class="form-control" name="q" value="{{ query }}" placeholder="Search by name or ingredients...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Dietary Restrictions</label>
                        <select class="form-select" name="dietary">
                            <option value="all">All Types</option>
                            {% for value, label in dietary_choices %}
                            <option value="{{ value }}" {% if selected_dietary == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Difficulty</label>
                        <select class="form-select" name="difficulty">
                            <option value="all">All Levels</option>
                            {% for value, label in difficulty_choices %}
                            <option value="{{ value }}" {% if selected_difficulty == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100">Filter</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Recipes -->
    <div class="row">
        {% for recipe in recipes %}
        <div class="col-md-4 mb-3">
            <div class="card h-100">
                <!-- Clean design - no images -->
                <div class="card-body">
                    <h5 class="card-title">
                        <a href="{% url 'recipe_detail' recipe.pk %}" class="text-decoration-none">{{ recipe.title }}</a>
                    </h5>
                    <div class="mb-2">
                        <span class="badge bg-{{ recipe.get_dietary_display_badge }}">{{ recipe.get_dietary_restrictions_display }}</span>
                        <span class="badge bg-secondary">{{ recipe.get_difficulty_display }}</span>
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-clock"></i> {{ recipe.cooking_time }} min
                        <span class="mx-2">•</span>
                        <i class="fas fa-users"></i> {{ recipe.servings }} servings
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <p>No recipes found. <a href="{% url 'recipe_create' %}">Add one!</a></p>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
