# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, <PERSON> <<EMAIL>>, 2016
# 코딩 영, 2021
# <PERSON><PERSON><PERSON> / <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> Ta<PERSON>uffe <<EMAIL>>, 2014,2016
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-11-10 02:10+0000\n"
"Last-Translator: 코딩 영\n"
"Language-Team: Korean (http://www.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Administrative Documentation"
msgstr "관리 문서"

msgid "Home"
msgstr "홈"

msgid "Documentation"
msgstr "문서"

msgid "Bookmarklets"
msgstr "북마크릿"

msgid "Documentation bookmarklets"
msgstr "문서 북마크릿"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"북마크릿을 설치하시려면 링크를 북마크 툴바로 드래그 하거나, 오른쪽 클릭으로 "
"해당 링크를 북마크에 추가하세요. 이제 사이트 내의 모든 페이지에서 북마크릿 선"
"택이 가능합니다."

msgid "Documentation for this page"
msgstr "이 페이지의 문서"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "각 페이지로에서 해당 페이지를 생성한 뷰의 문서로 갑니다."

msgid "Tags"
msgstr "태그"

msgid "List of all the template tags and their functions."
msgstr "모든 템플릿 태그와 함수의 목록"

msgid "Filters"
msgstr "필터"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr "필터는 템플릿의 변수에 적용되어 출력을 바꿀 수 있는 동작입니다."

msgid "Models"
msgstr "모델"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"모델은 모든 시스템에서 개체와 연관된 필드에 대한 설명입니다. 각 모델은 템플"
"릿 변수로 접근 할 수 있는 필드의 목록을 가지고 있습니다."

msgid "Views"
msgstr "뷰"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"일반 사이트의 각 페이지는 뷰에 의해 생성됩니다. 뷰는 페이지를 생성하는데 사용"
"되는 템플릿과 그 템플릿을 사용할 수 있게해주는 개체를 정의합니다."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "브라우저 도구는 신속하게 관리자 기능에 접근 할 수 있습니다."

msgid "Please install docutils"
msgstr "docutils를 설치해주세요."

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"관리자 문서 시스템은 파이썬의  <a href=\"%(link)s\">docutils</a> 라이브러리"
"를 필요로 합니다."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "관리자에게 <a href=\"%(link)s\">docutils</a> 설치를 요청해주세요."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "필드"

msgid "Field"
msgstr "필드"

msgid "Type"
msgstr "타입"

msgid "Description"
msgstr "설명"

msgid "Methods with arguments"
msgstr "아규먼트를 포함한 메소드"

msgid "Method"
msgstr "메소드"

msgid "Arguments"
msgstr "아규먼트"

msgid "Back to Model documentation"
msgstr "모델문서로 돌아가기"

msgid "Model documentation"
msgstr "모델 문서"

msgid "Model groups"
msgstr "모델 그룹"

msgid "Templates"
msgstr "템플릿"

#, python-format
msgid "Template: %(name)s"
msgstr "템플릿 : %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "템플릿: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "템플릿<q>%(name)s</q>에 대한 검색 경로:"

msgid "(does not exist)"
msgstr "(존재하지 않습니다)"

msgid "Back to Documentation"
msgstr "문서로 "

msgid "Template filters"
msgstr "템플릿 필터"

msgid "Template filter documentation"
msgstr "템플릿 필터 문서"

msgid "Built-in filters"
msgstr "내장 필터"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"이 필터를 사용하기 위해서는 필터를 사용하기 전에  템플릿에 <코드>%(code)s</코"
"드> 를 입력하세요."

msgid "Template tags"
msgstr "템플릿 태그"

msgid "Template tag documentation"
msgstr "템플릿 태그 문서"

msgid "Built-in tags"
msgstr "내장 태그"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"이 태그를 사용하기 위해서는 태그를 사용하기 전에 템플릿에 <code>%(code)s</"
"code> 를 입력하세요."

#, python-format
msgid "View: %(name)s"
msgstr "뷰: %(name)s"

msgid "Context:"
msgstr "컨텍스트 :"

msgid "Templates:"
msgstr "템플릿 :"

msgid "Back to View documentation"
msgstr "뷰 문서로 돌아가기"

msgid "View documentation"
msgstr "뷰 문서"

msgid "Jump to namespace"
msgstr "네임스페이스로"

msgid "Empty namespace"
msgstr "빈 네임스페이스"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "%(name)s 네임스페이스 뷰"

msgid "Views by empty namespace"
msgstr "빈 네임스페이스 뷰"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"뷰 함수: <code>%(full_name)s</code>. Name: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "태그:"

msgid "filter:"
msgstr "필터:"

msgid "view:"
msgstr "뷰:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "애플리케이션 %(app_label)r 을 찾을 수 없습니다."

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""
"모델 %(model_name)r 을 애플리케이션 %(app_label)r 에서 찾을 수 없습니다."

msgid "model:"
msgstr "모델:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "`%(app_label)s.%(data_type)s` 관련된 개체"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "`%(app_label)s.%(object_name)s` 관련된 개체들"

#, python-format
msgid "all %s"
msgstr "모든 %s"

#, python-format
msgid "number of %s"
msgstr "%s 의 수"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s 은/는 url 패턴의 개체가 아닙니다."
