# This file is distributed under the same license as the Django package.
#
# Translators:
# GunChleoc, 2015-2016
# GunChleoc, 2015
# GunChleoc, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-07-15 10:42+0000\n"
"Last-Translator: GunChleoc\n"
"Language-Team: Gaelic, Scottish (http://www.transifex.com/django/django/"
"language/gd/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: gd\n"
"Plural-Forms: nplurals=4; plural=(n==1 || n==11) ? 0 : (n==2 || n==12) ? 1 : "
"(n > 2 && n < 20) ? 2 : 3;\n"

msgid "Administrative Documentation"
msgstr "Docamaideadh na rianachd"

msgid "Home"
msgstr "Dhachaigh"

msgid "Documentation"
msgstr "Docamaideadh"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Bookmarklets an docamaididh"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Gus bookmarklet a stàladh, slaodaich an ceangal gu bàr-inneal nan comharran-"
"lìn agad no dèan briogadh deas air a’ cheangal is cuir e ris na comharran-"
"lìn agad. ’S urrainn dhut am bookmarklet a thaghadh o duilleag sam bith san "
"làrach an uairsin."

msgid "Documentation for this page"
msgstr "Docamaideadh airson na duilleige seo"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Gearraidh tu leum o duilleag sam bith dhan docamaideadh airson an t-"
"seallaidh a ghineas an duilleag sin."

msgid "Tags"
msgstr "Tagaichean"

msgid "List of all the template tags and their functions."
msgstr ""
"Liosta dhe thagaichean nan ùrlaran uile ’s dhe na foincseanan a th’ aca."

msgid "Filters"
msgstr "Criathragan"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"’S e gnìomhan a tha sna criathragan as urrainn dhut cur an sàs air "
"caochladairean ann an teamplaid gus an t-às-chur atharrachadh."

msgid "Models"
msgstr "Modailean"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"’S e tuairisgeulan air a h-uile oibseact san t-siostam ’s air na raointean "
"co-cheangailte riutha-san a tha sna modailean. Tha liosta dhe raointean aig "
"gach modail as urrainn dhut inntrigeadh mar chaochladairean teamplaide"

msgid "Views"
msgstr "Seallaidhean"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Thèid gach duilleag air an làrach phoblach a ghintinn le sealladh. Mìnichidh "
"an sealladh dè an teamplaid a thèid a chleachdadh gus an duilleag a ghintinn "
"agus dè na h-oibseactan as urrainn dhan teamplaid ud a chleachdadh."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Innealan airson a’ bhrabhsair agad gus gnìomhan nan rianairean inntrigeadh "
"gu luath."

msgid "Please install docutils"
msgstr "Stàlaich docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Tha siostam docamaideadh na rianachd feumach air an leabharlann <a href="
"\"%(link)s\">docutils</a> aig Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Iarr air na rianairean agad gun stàlaich iad <a href=\"%(link)s\">docutils</"
"a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modail: %(name)s"

msgid "Fields"
msgstr "Raointean"

msgid "Field"
msgstr "Raoin"

msgid "Type"
msgstr "Seòrsa"

msgid "Description"
msgstr "Tuairisgeul"

msgid "Methods with arguments"
msgstr "Modhan le argamaidean"

msgid "Method"
msgstr "Modh"

msgid "Arguments"
msgstr "Argamaidean"

msgid "Back to Model documentation"
msgstr "Till gu docamaideadh modail"

msgid "Model documentation"
msgstr "Docamaideadh modail"

msgid "Model groups"
msgstr "Buidhnean modail"

msgid "Templates"
msgstr "Teamplaidean"

#, python-format
msgid "Template: %(name)s"
msgstr "Teamplaid: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Teamplaid: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Lorg slighe airson teamplaid <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(chan eil e ann)"

msgid "Back to Documentation"
msgstr "Till gun docamaideadh"

msgid "Template filters"
msgstr "Criathragan teamplaid"

msgid "Template filter documentation"
msgstr "Docamaideadh air criathragan teamplaid"

msgid "Built-in filters"
msgstr "Criathragan ’na bhroinn"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Gus na criathragan seo a chleachdadh, cuir <code>%(code)s</code> san "
"teamplaid agad mus cleachd thu a’ chriathrag."

msgid "Template tags"
msgstr "Tagaichean teamplaid"

msgid "Template tag documentation"
msgstr "Docamaideadh air tagaichean teamplaid"

msgid "Built-in tags"
msgstr "Tagaichean ’na bhroinn"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Gus na tagaichean seo a chleachdadh, cuir <code>%(code)s</code> san "
"teamplaid agad mus cleachd thu an taga."

#, python-format
msgid "View: %(name)s"
msgstr "Sealladh: %(name)s"

msgid "Context:"
msgstr "Co-theacsa:"

msgid "Templates:"
msgstr "Teamplaidean:"

msgid "Back to View documentation"
msgstr "Till gu docamaideadh nan seallaidhean"

msgid "View documentation"
msgstr "Docamaideadh nan seallaidhean"

msgid "Jump to namespace"
msgstr "Gearr leum dhan ainm-spàs"

msgid "Empty namespace"
msgstr "Ainm-spàs falamh"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Seallaidhean a-rèir ainm-spàs %(name)s"

msgid "Views by empty namespace"
msgstr "Seallaidhean a-rèir an ainm-spàis fhalaimh"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Foincsean seallaidh: <code>%(full_name)s</code>. Ainm: <code>"
"%(url_name)s</code>.\n"

msgid "tag:"
msgstr "taga:"

msgid "filter:"
msgstr "criathrag:"

msgid "view:"
msgstr "sealladh:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Cha deach aplacaid %(app_label)r a lorg"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Cha deach modail %(model_name)r a lorg ann an aplacaid %(app_label)r"

msgid "model:"
msgstr "modail:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "an t-oibseact `%(app_label)s.%(data_type)s` dàimheach"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "na h-oibseactan `%(app_label)s.%(object_name)s` dàimheach"

#, python-format
msgid "all %s"
msgstr "a h-uile %s"

#, python-format
msgid "number of %s"
msgstr "àireamh dhe %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "Chan eil coltas oibseact urlpattern air %s"
