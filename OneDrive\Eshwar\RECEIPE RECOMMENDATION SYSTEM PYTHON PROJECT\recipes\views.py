"""
Recipe Recommender Platform - Views Module

This module contains the view functions for the Recipe Recommender Platform,
a comprehensive culinary discovery platform focused on authentic South Indian cuisine.
The views handle user interactions, recipe discovery, search functionality, and
user engagement features.

Key Features:
- South Indian recipe discovery and browsing
- Advanced search with dietary restrictions and cooking time filters
- Recipe detail views with engagement tracking
- User authentication and profile management
- Recipe creation and management for authenticated users
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth import login, logout
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from .models import Recipe
from .forms import RecipeForm

def home(request):
    """
    Home page view for Recipe Recommender Platform.

    Displays featured authentic South Indian recipes to welcome users
    and provide quick access to popular traditional dishes. The home page
    showcases the platform's focus on South Indian culinary heritage.

    Returns:
        Rendered home page template with featured South Indian recipes
    """
    # Curated list of authentic South Indian recipe names for featured display
    south_indian_names = [
        '<PERSON><PERSON><PERSON>', 'Simple Dosa', '<PERSON><PERSON><PERSON>', 'Easy Idli',
        'Coconut Rice', '<PERSON>sa<PERSON>', 'Simple Rasam', 'Quick Sambar', 'Sambar',
        'Medu Vada', 'Uttapam', 'Pongal', 'Curd Rice', 'Bisi Bele Bath',
        'Rava Upma', 'Pesarattu', 'Lemon Rice', 'Coconut Chutney',
        'Appam', 'Puttu', 'Paniyaram', 'Tomato Rice'
    ]

    # Display 6 featured South Indian recipes on home page
    recipes = Recipe.objects.filter(title__in=south_indian_names)[:6]
    return render(request, 'recipes/simple_home.html', {'recipes': recipes})

def recipe_list(request):
    """
    Recipe listing view with advanced search and filtering capabilities.

    Provides comprehensive recipe discovery for South Indian cuisine with
    multiple filtering options including dietary restrictions, cooking time,
    difficulty level, and popularity. Supports text search across recipe
    content and various sorting options.

    Args:
        request: HTTP request object with optional query parameters

    Returns:
        Rendered recipe list template with filtered and sorted recipes
    """
    # Start with all South Indian recipes in the database
    recipes = Recipe.objects.all()

    # Advanced text search across multiple recipe fields
    query = request.GET.get('q')
    if query:
        recipes = recipes.filter(
            Q(title__icontains=query) |
            Q(ingredients__icontains=query) |
            Q(instructions__icontains=query) |
            Q(description__icontains=query) |
            Q(cooking_tips__icontains=query)
        )

    # Filter by dietary restrictions
    dietary = request.GET.get('dietary')
    if dietary and dietary != 'all':
        recipes = recipes.filter(dietary_restrictions=dietary)

    # Filter by difficulty
    difficulty = request.GET.get('difficulty')
    if difficulty and difficulty != 'all':
        recipes = recipes.filter(difficulty=difficulty)

    # Filter by cooking time
    time_filter = request.GET.get('time')
    if time_filter == 'quick':
        recipes = recipes.filter(total_time__lte=30)
    elif time_filter == 'medium':
        recipes = recipes.filter(total_time__gt=30, total_time__lte=60)
    elif time_filter == 'long':
        recipes = recipes.filter(total_time__gt=60)

    # Filter by popularity
    popular = request.GET.get('popular')
    if popular == 'true':
        recipes = recipes.filter(view_count__gte=50)

    # Sorting options
    sort_by = request.GET.get('sort', 'title')
    if sort_by == 'popularity':
        recipes = recipes.order_by('-view_count')
    elif sort_by == 'rating':
        recipes = recipes.order_by('-rating_average')
    elif sort_by == 'time':
        recipes = recipes.order_by('total_time')
    elif sort_by == 'newest':
        recipes = recipes.order_by('-created_at')
    else:
        recipes = recipes.order_by('title')

    context = {
        'recipes': recipes,
        'query': query,
        'dietary_choices': Recipe.DIETARY_CHOICES,
        'difficulty_choices': Recipe.DIFFICULTY_CHOICES,
        'selected_dietary': dietary,
        'selected_difficulty': difficulty,
        'selected_time': time_filter,
        'selected_popular': popular,
        'selected_sort': sort_by,
    }
    return render(request, 'recipes/recipe_list.html', context)

def recipe_detail(request, pk):
    recipe = get_object_or_404(Recipe, pk=pk)

    # Increment view count
    recipe.increment_view_count()

    # Get reviews for this recipe (if review model exists)
    reviews = []
    user_review = None

    context = {
        'recipe': recipe,
        'reviews': reviews,
        'user_review': user_review,
    }

    return render(request, 'recipes/recipe_detail.html', context)

@login_required
def recipe_create(request):
    if request.method == 'POST':
        form = RecipeForm(request.POST)
        if form.is_valid():
            recipe = form.save(commit=False)
            recipe.author = request.user
            recipe.save()
            return redirect('recipe_list')
    else:
        form = RecipeForm()
    return render(request, 'recipes/recipe_form.html', {'form': form})

def test_page(request):
    total_recipes = Recipe.objects.count()
    return render(request, 'recipes/test_page.html', {'total_recipes': total_recipes})

def signup(request):
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            return redirect('home')
    else:
        form = UserCreationForm()
    return render(request, 'registration/signup.html', {'form': form})

def custom_logout(request):
    logout(request)
    return render(request, 'registration/logged_out.html')
