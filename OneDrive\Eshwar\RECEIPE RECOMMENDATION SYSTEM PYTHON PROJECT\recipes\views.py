"""
Recipe Recommender Platform - Views Module

This module contains the view functions for the Recipe Recommender Platform,
a comprehensive culinary discovery platform focused on authentic South Indian cuisine.
The views handle user interactions, recipe discovery, search functionality, and
user engagement features.

Key Features:
- South Indian recipe discovery and browsing
- Advanced search with dietary restrictions and cooking time filters
- Recipe detail views with engagement tracking
- User authentication and profile management
- Recipe creation and management for authenticated users
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth import login, logout
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from .models import Recipe, UserProfile, CookingActivity
from .forms import RecipeForm, UserProfileForm, UserUpdateForm, CookingActivityForm

def home(request):
    """
    Home page view for Recipe Recommender Platform.

    Displays featured authentic South Indian recipes to welcome users
    and provide quick access to popular traditional dishes. The home page
    showcases the platform's focus on South Indian culinary heritage.

    Returns:
        Rendered home page template with featured South Indian recipes
    """
    # Curated list of authentic South Indian recipe names for featured display
    south_indian_names = [
        '<PERSON><PERSON><PERSON>sa', 'Simple Dosa', 'Idli Sambar', '<PERSON> Idli',
        'Coconut Rice', 'Rasam', 'Simple Rasam', 'Quick Sambar', 'Sambar',
        'Medu Vada', 'Uttapam', 'Pongal', 'Curd Rice', 'Bisi Bele Bath',
        'Rava Upma', 'Pesarattu', 'Lemon Rice', 'Coconut Chutney',
        'Appam', 'Puttu', 'Paniyaram', 'Tomato Rice'
    ]

    # Display 6 featured South Indian recipes on home page
    recipes = Recipe.objects.filter(title__in=south_indian_names)[:6]
    return render(request, 'recipes/simple_home.html', {'recipes': recipes})

def recipe_list(request):
    """
    Recipe listing view with advanced search and filtering capabilities.

    Provides comprehensive recipe discovery for South Indian cuisine with
    multiple filtering options including dietary restrictions, cooking time,
    difficulty level, and popularity. Supports text search across recipe
    content and various sorting options.

    Args:
        request: HTTP request object with optional query parameters

    Returns:
        Rendered recipe list template with filtered and sorted recipes
    """
    # Start with all South Indian recipes in the database
    recipes = Recipe.objects.all()

    # Advanced text search across multiple recipe fields
    query = request.GET.get('q')
    if query:
        recipes = recipes.filter(
            Q(title__icontains=query) |
            Q(ingredients__icontains=query) |
            Q(instructions__icontains=query) |
            Q(description__icontains=query) |
            Q(cooking_tips__icontains=query)
        )

    # Filter by category (based on recipe title keywords)
    category = request.GET.get('category')
    if category and category != 'all':
        category_keywords = {
            'breakfast': ['dosa', 'idli', 'uttapam', 'upma', 'poha', 'rava'],
            'main_course': ['sambar', 'rasam', 'bisi bele', 'puliyodarai', 'curd rice'],
            'snacks': ['vada', 'bonda', 'bajji', 'murukku', 'mixture', 'pakoda'],
            'desserts': ['payasam', 'mysore pak', 'holige', 'kesari', 'laddu', 'halwa'],
            'beverages': ['filter coffee', 'masala chai', 'buttermilk', 'panaka'],
            'rice_dishes': ['rice', 'biryani', 'pulao', 'pongal', 'chitranna'],
            'curry_dishes': ['curry', 'kootu', 'poriyal', 'thoran', 'palya'],
            'festival': ['festival', 'special', 'traditional', 'celebration']
        }

        if category in category_keywords:
            keywords = category_keywords[category]
            q_objects = Q()
            for keyword in keywords:
                q_objects |= Q(title__icontains=keyword)
            recipes = recipes.filter(q_objects)

    # Filter by dietary restrictions
    dietary = request.GET.get('dietary')
    if dietary and dietary != 'all':
        recipes = recipes.filter(dietary_restrictions=dietary)

    # Filter by difficulty
    difficulty = request.GET.get('difficulty')
    if difficulty and difficulty != 'all':
        recipes = recipes.filter(difficulty=difficulty)

    # Filter by cooking time
    time_filter = request.GET.get('time')
    if time_filter == 'quick':
        recipes = recipes.filter(total_time__lte=30)
    elif time_filter == 'medium':
        recipes = recipes.filter(total_time__gt=30, total_time__lte=60)
    elif time_filter == 'long':
        recipes = recipes.filter(total_time__gt=60)

    # Filter by popularity
    popular = request.GET.get('popular')
    if popular == 'true':
        recipes = recipes.filter(view_count__gte=50)

    # Sorting options
    sort_by = request.GET.get('sort', 'title')
    if sort_by == 'popularity':
        recipes = recipes.order_by('-view_count')
    elif sort_by == 'rating':
        recipes = recipes.order_by('-rating_average')
    elif sort_by == 'time':
        recipes = recipes.order_by('total_time')
    elif sort_by == 'newest':
        recipes = recipes.order_by('-created_at')
    else:
        recipes = recipes.order_by('title')

    # Define category choices for South Indian cuisine
    category_choices = [
        ('breakfast', '🥞 Breakfast Items'),
        ('main_course', '🍛 Main Course'),
        ('snacks', '🍿 Snacks & Appetizers'),
        ('desserts', '🍰 Sweets & Desserts'),
        ('beverages', '☕ Traditional Beverages'),
        ('rice_dishes', '🍚 Rice Dishes'),
        ('curry_dishes', '🍛 Curry & Gravy'),
        ('festival', '🎭 Festival Specials'),
    ]

    context = {
        'recipes': recipes,
        'query': query,
        'current_query': query,
        'categories': category_choices,
        'difficulties': Recipe.DIFFICULTY_CHOICES,
        'dietary_choices': Recipe.DIETARY_CHOICES,
        'difficulty_choices': Recipe.DIFFICULTY_CHOICES,
        'selected_category': category,
        'current_category': category,
        'selected_dietary': dietary,
        'selected_difficulty': difficulty,
        'current_difficulty': difficulty,
        'selected_time': time_filter,
        'selected_popular': popular,
        'selected_sort': sort_by,
    }
    return render(request, 'recipes/recipe_list.html', context)

def recipe_detail(request, pk):
    recipe = get_object_or_404(Recipe, pk=pk)

    # Increment view count
    recipe.increment_view_count()

    # Get reviews for this recipe (if review model exists)
    reviews = []
    user_review = None

    context = {
        'recipe': recipe,
        'reviews': reviews,
        'user_review': user_review,
    }

    return render(request, 'recipes/recipe_detail.html', context)

@login_required
def recipe_create(request):
    if request.method == 'POST':
        form = RecipeForm(request.POST)
        if form.is_valid():
            recipe = form.save(commit=False)
            recipe.author = request.user
            recipe.save()
            return redirect('recipe_list')
    else:
        form = RecipeForm()
    return render(request, 'recipes/recipe_form.html', {'form': form})



def signup(request):
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            return redirect('home')
    else:
        form = UserCreationForm()
    return render(request, 'registration/signup.html', {'form': form})

def custom_logout(request):
    logout(request)
    return render(request, 'registration/logged_out.html')


@login_required
def profile_view(request):
    """
    User profile view displaying comprehensive profile information.

    Shows user details, cooking statistics, favorite recipes, recent activities,
    and personalized recommendations for South Indian cuisine.
    """
    # Get or create user profile
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    # Get user's favorite recipes
    favorite_recipes = profile.favorite_recipes.all()[:6]  # Show 6 recent favorites

    # Get user's contributed recipes
    contributed_recipes = Recipe.objects.filter(author=request.user)

    # Get recent cooking activities
    recent_activities = CookingActivity.objects.filter(user=request.user)[:10]

    # Calculate cooking statistics
    cooking_stats = {
        'total_favorites': profile.get_favorite_count(),
        'total_contributed': contributed_recipes.count(),
        'total_activities': recent_activities.count(),
        'cooking_level': profile.get_cooking_skill_display(),
        'dietary_preference': profile.get_preferred_dietary_display(),
    }

    # Get personalized recipe recommendations based on dietary preferences
    recommended_recipes = Recipe.objects.filter(
        dietary_restrictions=profile.preferred_dietary
    ).exclude(
        id__in=favorite_recipes.values_list('id', flat=True)
    )[:4] if profile.preferred_dietary != 'none' else Recipe.objects.all()[:4]

    context = {
        'profile': profile,
        'favorite_recipes': favorite_recipes,
        'contributed_recipes': contributed_recipes,
        'recent_activities': recent_activities,
        'cooking_stats': cooking_stats,
        'recommended_recipes': recommended_recipes,
        'page_title': f'{request.user.username}\'s Profile'
    }

    return render(request, 'recipes/profile.html', context)


@login_required
def profile_edit(request):
    """
    Edit user profile view.

    Allows users to update their profile information including bio,
    cooking skill level, dietary preferences, and personal details.
    """
    # Get or create user profile
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        user_form = UserUpdateForm(request.POST, instance=request.user)
        profile_form = UserProfileForm(request.POST, instance=profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()

            # Add success message
            from django.contrib import messages
            messages.success(request, 'Your profile has been updated successfully!')
            return redirect('profile_view')
    else:
        user_form = UserUpdateForm(instance=request.user)
        profile_form = UserProfileForm(instance=profile)

    context = {
        'user_form': user_form,
        'profile_form': profile_form,
        'page_title': 'Edit Profile'
    }

    return render(request, 'recipes/profile_edit.html', context)


@login_required
def add_to_favorites(request, pk):
    """
    Add or remove recipe from user's favorites.

    Toggles favorite status for a recipe and updates user's favorite collection.
    """
    recipe = get_object_or_404(Recipe, id=pk)
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if recipe in profile.favorite_recipes.all():
        # Remove from favorites
        profile.favorite_recipes.remove(recipe)
        favorited = False

        # Log activity
        CookingActivity.objects.create(
            user=request.user,
            recipe=recipe,
            activity_type='favorite',
            notes='Removed from favorites'
        )
    else:
        # Add to favorites
        profile.favorite_recipes.add(recipe)
        favorited = True

        # Log activity
        CookingActivity.objects.create(
            user=request.user,
            recipe=recipe,
            activity_type='favorite',
            notes='Added to favorites'
        )

    # Return JSON response for AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        from django.http import JsonResponse
        return JsonResponse({
            'favorited': favorited,
            'favorite_count': profile.get_favorite_count()
        })

    # Redirect back to recipe detail page
    return redirect('recipe_detail', pk=recipe.id)


@login_required
def cooking_activity_log(request, pk):
    """
    Log cooking activity for a recipe.

    Allows users to record when they've tried a recipe and share their experience.
    """
    recipe = get_object_or_404(Recipe, id=pk)

    if request.method == 'POST':
        form = CookingActivityForm(request.POST)
        if form.is_valid():
            activity = form.save(commit=False)
            activity.user = request.user
            activity.recipe = recipe
            activity.save()

            # Update user profile statistics
            profile, created = UserProfile.objects.get_or_create(user=request.user)
            if activity.activity_type == 'cook':
                profile.total_recipes_tried += 1
                profile.save()

            from django.contrib import messages
            messages.success(request, 'Your cooking activity has been logged!')
            return redirect('recipe_detail', pk=recipe.id)
    else:
        form = CookingActivityForm()

    context = {
        'form': form,
        'recipe': recipe,
        'page_title': f'Log Activity for {recipe.title}'
    }

    return render(request, 'recipes/cooking_activity_form.html', context)
