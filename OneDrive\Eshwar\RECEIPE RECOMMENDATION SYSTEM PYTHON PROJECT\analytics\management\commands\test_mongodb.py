"""
Django management command to test MongoDB connectivity and functionality.
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import pymongo
from pymongo import MongoClient
import datetime


class Command(BaseCommand):
    help = 'Test MongoDB connectivity and basic operations for Recipe Recommender'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )
        parser.add_argument(
            '--create-sample',
            action='store_true',
            help='Create sample data in MongoDB',
        )

    def handle(self, *args, **options):
        """Main command handler."""
        self.verbose = options['verbose']
        self.create_sample = options['create_sample']
        
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing MongoDB Connection for Recipe Recommender')
        )
        self.stdout.write('=' * 60)
        
        try:
            # Test MongoDB connection
            self.test_connection()
            
            # Test database operations
            self.test_database_operations()
            
            # Create sample data if requested
            if self.create_sample:
                self.create_sample_data()
            
            # Test collections
            self.test_collections()
            
            # Final summary
            self.show_summary()
            
        except Exception as e:
            raise CommandError(f'MongoDB test failed: {str(e)}')

    def test_connection(self):
        """Test basic MongoDB connection."""
        self.stdout.write('\n1️⃣ Testing MongoDB Connection...')
        
        try:
            # Get MongoDB settings
            mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})
            host = mongodb_settings.get('host', 'mongodb://localhost:27017/')
            
            if self.verbose:
                self.stdout.write(f'   Connecting to: {host}')
            
            # Create MongoDB client
            self.client = MongoClient(host, serverSelectionTimeoutMS=5000)
            
            # Test connection
            self.client.admin.command('ping')
            
            self.stdout.write(
                self.style.SUCCESS('   ✅ MongoDB connection successful!')
            )
            
            # Get server info
            server_info = self.client.server_info()
            self.stdout.write(f'   📊 MongoDB Version: {server_info["version"]}')
            
        except pymongo.errors.ServerSelectionTimeoutError:
            raise CommandError('❌ Cannot connect to MongoDB. Is MongoDB running?')
        except Exception as e:
            raise CommandError(f'❌ MongoDB connection error: {str(e)}')

    def test_database_operations(self):
        """Test database operations."""
        self.stdout.write('\n2️⃣ Testing Database Operations...')
        
        try:
            # Get database settings
            mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})
            db_name = mongodb_settings.get('db_name', 'recipe_recommender_db')
            
            # Connect to database
            self.db = self.client[db_name]
            
            if self.verbose:
                self.stdout.write(f'   Database: {db_name}')
            
            # List existing collections
            collections = self.db.list_collection_names()
            self.stdout.write(f'   📁 Existing collections: {len(collections)}')
            
            if self.verbose and collections:
                for collection in collections:
                    count = self.db[collection].count_documents({})
                    self.stdout.write(f'      - {collection}: {count} documents')
            
            self.stdout.write(
                self.style.SUCCESS('   ✅ Database operations successful!')
            )
            
        except Exception as e:
            raise CommandError(f'❌ Database operation error: {str(e)}')

    def test_collections(self):
        """Test collection operations."""
        self.stdout.write('\n3️⃣ Testing Collection Operations...')
        
        try:
            # Get collection settings
            mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})
            collections_config = mongodb_settings.get('collections', {})
            
            for purpose, collection_name in collections_config.items():
                collection = self.db[collection_name]
                
                # Test basic operations
                test_doc = {
                    'test': True,
                    'purpose': purpose,
                    'timestamp': datetime.datetime.utcnow(),
                    'message': f'Test document for {purpose}'
                }
                
                # Insert test document
                result = collection.insert_one(test_doc)
                
                # Find the document
                found_doc = collection.find_one({'_id': result.inserted_id})
                
                # Delete test document
                collection.delete_one({'_id': result.inserted_id})
                
                if found_doc:
                    self.stdout.write(f'   ✅ {collection_name} ({purpose}): CRUD operations work')
                else:
                    self.stdout.write(f'   ❌ {collection_name} ({purpose}): CRUD operations failed')
            
            self.stdout.write(
                self.style.SUCCESS('   ✅ Collection operations successful!')
            )
            
        except Exception as e:
            raise CommandError(f'❌ Collection operation error: {str(e)}')

    def create_sample_data(self):
        """Create sample data for testing."""
        self.stdout.write('\n4️⃣ Creating Sample Data...')
        
        try:
            # Sample recipe analytics data
            recipes_collection = self.db['recipes']
            sample_recipes = [
                {
                    'recipe_id': 1,
                    'title': 'Masala Dosa',
                    'category': 'breakfast',
                    'views': 150,
                    'ratings': [4.5, 5.0, 4.0, 4.5],
                    'search_keywords': ['dosa', 'masala', 'south indian', 'breakfast'],
                    'created_at': datetime.datetime.utcnow()
                },
                {
                    'recipe_id': 2,
                    'title': 'Chettinad Chicken Curry',
                    'category': 'non_veg',
                    'views': 89,
                    'ratings': [4.8, 4.5, 5.0],
                    'search_keywords': ['chicken', 'curry', 'chettinad', 'non-veg'],
                    'created_at': datetime.datetime.utcnow()
                }
            ]
            
            # Insert sample recipes
            recipes_collection.insert_many(sample_recipes)
            self.stdout.write('   ✅ Sample recipe analytics created')
            
            # Sample user activity data
            activity_collection = self.db['user_activity']
            sample_activities = [
                {
                    'user_id': 1,
                    'action': 'view_recipe',
                    'recipe_id': 1,
                    'timestamp': datetime.datetime.utcnow(),
                    'session_id': 'test_session_1'
                },
                {
                    'user_id': 1,
                    'action': 'search',
                    'query': 'chicken curry',
                    'results_count': 5,
                    'timestamp': datetime.datetime.utcnow(),
                    'session_id': 'test_session_1'
                }
            ]
            
            # Insert sample activities
            activity_collection.insert_many(sample_activities)
            self.stdout.write('   ✅ Sample user activity created')
            
            self.stdout.write(
                self.style.SUCCESS('   ✅ Sample data creation successful!')
            )
            
        except Exception as e:
            raise CommandError(f'❌ Sample data creation error: {str(e)}')

    def show_summary(self):
        """Show final summary."""
        self.stdout.write('\n📊 MongoDB Test Summary')
        self.stdout.write('=' * 30)
        
        try:
            # Database info
            mongodb_settings = getattr(settings, 'MONGODB_SETTINGS', {})
            db_name = mongodb_settings.get('db_name', 'recipe_recommender_db')
            
            self.stdout.write(f'🗄️  Database: {db_name}')
            
            # Collection summary
            collections = self.db.list_collection_names()
            self.stdout.write(f'📁 Collections: {len(collections)}')
            
            total_documents = 0
            for collection_name in collections:
                count = self.db[collection_name].count_documents({})
                total_documents += count
                self.stdout.write(f'   - {collection_name}: {count} documents')
            
            self.stdout.write(f'📄 Total documents: {total_documents}')
            
            # Connection info
            server_info = self.client.server_info()
            self.stdout.write(f'🔗 MongoDB version: {server_info["version"]}')
            
            self.stdout.write('\n🎉 MongoDB is ready for Recipe Recommender!')
            self.stdout.write('✅ Connection: Working')
            self.stdout.write('✅ Database: Accessible')
            self.stdout.write('✅ Collections: Functional')
            self.stdout.write('✅ CRUD Operations: Working')
            
        except Exception as e:
            self.stdout.write(f'❌ Summary error: {str(e)}')
        finally:
            # Close connection
            if hasattr(self, 'client'):
                self.client.close()
                if self.verbose:
                    self.stdout.write('\n🔌 MongoDB connection closed')
