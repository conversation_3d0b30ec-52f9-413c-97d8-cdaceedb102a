"""
Django settings for Recipe Recommender Platform.

A comprehensive culinary discovery platform designed to help home cooks,
food enthusiasts, and culinary professionals discover and share authentic
South Indian recipes. This platform uses a Python-only approach with
server-side processing, eliminating JavaScript dependencies while providing
advanced recipe discovery, dietary accommodation, and personalized
recommendations through a dual database architecture (SQLite + MongoDB).

Key Features:
- Pure Python backend with Django framework
- No JavaScript dependencies (CSS-only Bootstrap)
- Server-side analytics and data processing
- MongoDB integration via PyMongo
- Responsive design using Django templates

Generated by 'django-admin startproject' using Django 3.1.12.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'rbhgy(w#820ix0v$zq@77i@$n9u9zh(cs5-o28#&h01#0^&lm3'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition
# Recipe Recommender Platform Applications

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'recipes',         # Core recipe management and South Indian cuisine
    'analytics',       # User engagement analytics and MongoDB integration
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'recipe_recommender.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'recipe_recommender.wsgi.application'


# Database Configuration
# Dual Database Architecture for Recipe Recommender Platform
# https://docs.djangoproject.com/en/3.1/ref/settings/#databases

# Primary Database: SQLite for Django ORM (User auth, core recipe data)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Secondary Database: MongoDB for Analytics and Advanced Search
# Handles recipe analytics, user behavior tracking, and search optimization
MONGODB_SETTINGS = {
    'host': 'mongodb://localhost:27017/',
    'db_name': 'recipe_recommender_db',
    'collections': {
        'recipes': 'recipes',           # Recipe analytics and search data
        'user_activity': 'user_activity',  # User engagement tracking
        'recommendations': 'recommendations'  # Personalized recommendations
    }
}


# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript)
# https://docs.djangoproject.com/en/3.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'


# Add this at the bottom of the file
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Login/Logout redirects
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'
LOGIN_URL = '/accounts/login/'

# No media files needed - keeping it simple without images
